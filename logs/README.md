# 日志目录

此目录用于存储应用程序的日志文件。

## 日志文件

应用程序会在此目录中创建以下日志文件：

- `default.log` - 主要应用日志
- `debug.log` - 调试信息日志
- `gunicorn.log` - Gunicorn Web服务器标准输出日志
- `gunicorn_error.log` - Gunicorn Web服务器错误日志
- `qcluster.log` - Django Q任务队列标准输出日志
- `qcluster_error.log` - Django Q任务队列错误日志

注意：Supervisor进程管理器的日志现在输出到stdout，可以通过Docker日志查看。

## 注意事项

- 请勿删除此目录，否则应用程序启动时会报错
- 日志文件会自动轮转，默认保留5个备份文件
- 每个日志文件最大为5MB
- 在Docker容器中，此目录权限设置为777，允许root和appuser都可以写入

## 权限说明

在Docker环境中：
- supervisord以root身份运行，日志输出到stdout
- Django应用和任务队列以root身份运行（容器化环境中的安全实践）
- logs目录权限设置为777，确保所有进程都可以写入日志文件

## 日志配置

日志配置位于 `config/settings.py` 文件中的 `LOGGING` 部分。
