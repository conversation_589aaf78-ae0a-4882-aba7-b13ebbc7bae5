from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'strategy', views.StrategyViewSet, basename="strategy")
router.register(r'email_template', views.EmailTemplateViewSet, basename="email_template")
router.register(r'email_template_file', views.EmailTemplateFileViewSet, basename="email_template_file")
# 钓鱼页面
router.register(r'phishing_page', views.PhishingPageViewSet, basename="phishing_page")
# 克隆网站
router.register(r'request_url', views.RequestUrlViewSet, basename="request_url")
# 解析eml
router.register(r'parser_eml', views.ParserEmlViewSet, basename="parser_eml")
# 邮件任务
router.register(r'email_task', views.EmailTaskViewSet, basename="email_task")
# 表单信息
router.register(r'exercise/(?P<exercise_id>\d+)/from_data', views.FormDataViewSet, basename="email_form_data")


urlpatterns = [
    path('', include(router.urls)),
]
