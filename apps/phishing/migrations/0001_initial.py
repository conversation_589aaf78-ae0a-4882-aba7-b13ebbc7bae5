# Generated by Django 5.1.3 on 2024-12-09 09:15

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MailHeaderModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('x_custom_header', models.CharField(max_length=200, verbose_name='X-Custom-Header')),
                ('gophish', models.CharField(max_length=200, verbose_name='{{.URL}}-gophish')),
            ],
            options={
                'verbose_name': '自定义邮件头',
                'verbose_name_plural': '自定义邮件头',
                'db_table': 'ls_mail_header',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='StrategyModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='策略名称')),
                ('api_type', models.CharField(choices=[('SFTP', 'SFTP'), ('POP3', 'POP3'), ('IMAP', 'IMAP'), ('WEBMAIL', 'WEBMAIL')], max_length=10, verbose_name='接口类型')),
                ('sender_email', models.CharField(max_length=100, verbose_name='发件人邮箱')),
                ('email_server_address', models.CharField(max_length=100, verbose_name='邮箱服务器地址')),
                ('email_server_account', models.CharField(max_length=100, verbose_name='邮箱服务器账号')),
                ('email_server_pwd', models.CharField(max_length=100, verbose_name='邮箱服务器密码')),
                ('is_ignore_certificate_errors', models.BooleanField(default=False, verbose_name='是否忽略证书错误')),
                ('mail_headers', models.ManyToManyField(blank=True, db_table='ls_strategy_to_mail_header', to='phishing.mailheadermodel', verbose_name='自定义邮件头')),
            ],
            options={
                'verbose_name': '发送邮件策略',
                'verbose_name_plural': '发送邮件策略',
                'db_table': 'ls_strategy',
                'ordering': ['-id'],
            },
        ),
    ]
