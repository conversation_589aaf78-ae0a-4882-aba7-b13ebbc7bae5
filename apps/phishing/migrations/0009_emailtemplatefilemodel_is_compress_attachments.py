# Generated by Django 5.1.3 on 2024-12-16 11:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0008_alter_emailtemplatefilemodel_email_file'),
    ]

    operations = [
        migrations.AddField(
            model_name='emailtemplatefilemodel',
            name='is_compress_attachments',
            field=models.BooleanField(default=False, verbose_name='是否压缩附件'),
        ),
    ]
