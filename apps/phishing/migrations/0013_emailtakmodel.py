# Generated by Django 5.1.3 on 2024-12-19 14:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0012_phishingpagemodel'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTakModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('phishing_link', models.CharField(blank=True, max_length=300, null=True, verbose_name='钓鱼链接')),
                ('email_template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_task', to='phishing.emailtemplatemodel', verbose_name='邮件模板')),
                ('phishing_page', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_task', to='phishing.phishingpagemodel', verbose_name='钓鱼页面')),
                ('strategy', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='email_task', to='phishing.strategymodel', verbose_name='发送策略')),
            ],
            options={
                'verbose_name': '邮件任务',
                'verbose_name_plural': '邮件任务',
                'db_table': 'ls_email_task',
                'ordering': ['-id'],
            },
        ),
    ]
