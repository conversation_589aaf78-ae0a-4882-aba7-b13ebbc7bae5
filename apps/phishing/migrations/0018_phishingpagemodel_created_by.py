# Generated by Django 5.1.3 on 2025-02-12 16:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0017_emailtemplatemodel_created_by'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='phishingpagemodel',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='phishing_pages', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
    ]
