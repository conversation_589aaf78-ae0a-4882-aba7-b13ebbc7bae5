# Generated by Django 5.1.3 on 2025-01-16 15:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0014_remove_emailtemplatemodel_email_server_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('recipient', models.EmailField(max_length=254, verbose_name='收件人')),
                ('status', models.CharField(choices=[('PENDING', '待发送'), ('SENDING', '发送中'), ('SUCCESS', '发送成功'), ('FAILED', '发送失败'), ('RETRY', '重试中')], default='PENDING', max_length=20, verbose_name='发送状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('retry_count', models.IntegerField(default=0, verbose_name='重试次数')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='发送时间')),
                ('task_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='任务ID')),
                ('email_task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_logs', to='phishing.emailtakmodel', verbose_name='邮件任务')),
            ],
            options={
                'verbose_name': '邮件发送记录',
                'verbose_name_plural': '邮件发送记录',
                'db_table': 'ls_email_log',
                'ordering': ['-created_at'],
            },
        ),
    ]
