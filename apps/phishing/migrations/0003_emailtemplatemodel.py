# Generated by Django 5.1.3 on 2024-12-10 07:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('phishing', '0002_alter_strategymodel_api_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplateModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='邮件模板名称')),
                ('email_subject', models.CharField(max_length=100, verbose_name='邮件模板主题')),
                ('sender', models.CharField(max_length=100, verbose_name='邮件模板发件人')),
                ('email_server', models.CharField(max_length=100, verbose_name='邮件模板邮件服务器')),
                ('content', models.TextField(blank=True, null=True, verbose_name='邮件模板内容')),
            ],
            options={
                'verbose_name': '邮件模板',
                'verbose_name_plural': '邮件模板',
                'db_table': 'ls_email_template',
                'ordering': ['-id'],
            },
        ),
    ]
