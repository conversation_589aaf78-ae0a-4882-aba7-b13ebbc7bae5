from django.utils import timezone
from .models import DataLeakage
from apps.dashboard.utils import DataFormatter
import json

class DataLeakageReportGenerator:
    """数据泄露报告生成器"""

    @staticmethod
    def generate_report(exercise_id=None):
        """生成数据泄露报告"""
        queryset = DataLeakage.objects.all()
        if exercise_id:
            queryset = queryset.filter(infection_record__exercise_id=exercise_id)

        report_data = []
        for leakage in queryset:
            report_data.append({
                'exercise': {
                    'id': leakage.infection_record.exercise.id,
                    'name': leakage.infection_record.exercise.name
                },
                'asset': {
                    'id': leakage.infection_record.asset.id,
                    'name': leakage.infection_record.asset.name
                },
                'leakage_type': leakage.get_leakage_type_display(),
                'file_count': leakage.file_count,
                'data_size': DataFormatter.format_bytes(leakage.data_size),
                'sensitivity': leakage.sensitivity,
                'file_types': leakage.file_types,
                'detected_time': leakage.detected_time.isoformat(),
                'description': leakage.description
            })

        return json.dumps(report_data, ensure_ascii=False, indent=2)

    @staticmethod
    def generate_summary(exercise_id=None):
        """生成数据泄露摘要"""
        queryset = DataLeakage.objects.all()
        if exercise_id:
            queryset = queryset.filter(infection_record__exercise_id=exercise_id)

        total_leakages = queryset.count()
        total_files = queryset.aggregate(total=Sum('file_count'))['total'] or 0
        total_size = queryset.aggregate(total=Sum('data_size'))['total'] or 0
        avg_sensitivity = queryset.aggregate(avg=Avg('sensitivity'))['avg'] or 0

        summary = {
            'total_leakages': total_leakages,
            'total_files': total_files,
            'total_size': DataFormatter.format_bytes(total_size),
            'avg_sensitivity': avg_sensitivity
        }

        return summary 