from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import DeviceViewSet, InfectionRecordViewSet, DeviceCommandViewSet

# 创建路由器
router = DefaultRouter()
router.register('devices', DeviceViewSet)
router.register('records', InfectionRecordViewSet)
router.register(r'device-commands', DeviceCommandViewSet)

# URL patterns
urlpatterns = [
    path('', include(router.urls)),
]
