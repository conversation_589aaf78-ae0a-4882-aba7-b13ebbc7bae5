from django_filters import rest_framework as filters
from .models import Device, InfectionRecord, DeviceCommand


class DeviceFilter(filters.FilterSet):
    """设备过滤器"""
    device_id = filters.CharFilter(lookup_expr='exact')
    infection_count = filters.NumberFilter(field_name="infection_count", lookup_expr='gt', label="感染次数大于")
    
    class Meta:
        model = Device
        fields = ['device_id', 'exercise_id', 'infection_count']


class InfectionRecordFilter(filters.FilterSet):
    """感染记录过滤器"""
    device_id = filters.CharFilter(field_name='device__device_id', lookup_expr='exact')
    
    class Meta:
        model = InfectionRecord
        fields = ['device_id', 'exercise_id']


class DeviceCommandFilter(filters.FilterSet):
    """设备命令过滤器"""
    device_id = filters.CharFilter(field_name='device__device_id', lookup_expr='exact')
    
    class Meta:
        model = DeviceCommand
        fields = ['device_id', 'exercise_id']
