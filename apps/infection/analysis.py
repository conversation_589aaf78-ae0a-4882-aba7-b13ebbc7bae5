from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import timedelta
from .models import InfectionRecord, DataLeakage
from apps.dashboard.utils import DataFormatter

class InfectionAnalyzer:
    """感染情况分析工具类
    
    提供感染数据的分析和统计功能
    """
    
    @staticmethod
    def get_infection_overview(exercise_id=None, start_date=None, end_date=None):
        """获取感染概况
        
        Args:
            exercise_id: 演练ID（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
        """
        queryset = InfectionRecord.objects.all()
        
        # 应用过滤条件
        if exercise_id:
            queryset = queryset.filter(exercise_id=exercise_id)
        if start_date:
            queryset = queryset.filter(infection_time__gte=start_date)
        if end_date:
            queryset = queryset.filter(infection_time__lte=end_date)
        
        # 计算统计数据
        stats = {
            'total_infected': queryset.count(),
            'total_encrypted_files': queryset.aggregate(
                total=Sum('encrypted_files')
            )['total'] or 0,
            'total_data_loss': queryset.aggregate(
                total=Sum('data_loss')
            )['total'] or 0,
            'status_distribution': dict(
                queryset.values('status')
                .annotate(count=Count('id'))
                .values_list('status', 'count')
            ),
            'department_distribution': dict(
                queryset.values('asset__department')
                .annotate(count=Count('id'))
                .values_list('asset__department', 'count')
            ),
            'asset_type_distribution': dict(
                queryset.values('asset__asset_type')
                .annotate(count=Count('id'))
                .values_list('asset__asset_type', 'count')
            )
        }
        
        return stats
    
    @staticmethod
    def get_infection_trends(days=7, exercise_id=None):
        """获取感染趋势数据
        
        Args:
            days: 统计天数
            exercise_id: 演练ID（可选）
        """
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        queryset = InfectionRecord.objects.filter(
            infection_time__range=(start_date, end_date)
        )
        
        if exercise_id:
            queryset = queryset.filter(exercise_id=exercise_id)
        
        # 按天统计
        trends = queryset.values('infection_time__date').annotate(
            infected_count=Count('id'),
            encrypted_count=Count('id', filter=Q(status='EN')),
            data_loss=Sum('data_loss')
        ).order_by('infection_time__date')
        
        return [{
            'date': item['infection_time__date'],
            'infected_count': item['infected_count'],
            'encrypted_count': item['encrypted_count'],
            'data_loss': DataFormatter.format_bytes(item['data_loss'] or 0)
        } for item in trends]
    
    @staticmethod
    def get_data_leakage_analysis(exercise_id=None):
        """获取数据泄露分析
        
        Args:
            exercise_id: 演练ID（可选）
        """
        queryset = DataLeakage.objects.all()
        
        if exercise_id:
            queryset = queryset.filter(infection_record__exercise_id=exercise_id)
        
        analysis = {
            'total_leakages': queryset.count(),
            'total_files': queryset.aggregate(
                total=Sum('file_count')
            )['total'] or 0,
            'total_size': queryset.aggregate(
                total=Sum('data_size')
            )['total'] or 0,
            'avg_sensitivity': queryset.aggregate(
                avg=Avg('sensitivity')
            )['avg'] or 0,
            'type_distribution': dict(
                queryset.values('leakage_type')
                .annotate(count=Count('id'))
                .values_list('leakage_type', 'count')
            ),
            'sensitivity_distribution': dict(
                queryset.values('sensitivity')
                .annotate(count=Count('id'))
                .values_list('sensitivity', 'count')
            ),
            'file_type_summary': self._aggregate_file_types(queryset)
        }
        
        return analysis
    
    @staticmethod
    def _aggregate_file_types(queryset):
        """汇总文件类型统计
        
        将所有记录的file_types字段合并统计
        """
        file_types = {}
        for record in queryset:
            for file_type, count in record.file_types.items():
                file_types[file_type] = file_types.get(file_type, 0) + count
        return file_types
    
    @staticmethod
    def get_high_risk_assets(threshold=8):
        """获取高风险资产
        
        Args:
            threshold: 敏感度阈值（默认8）
        """
        # 查找包含高敏感度数据泄露的资产
        high_risk_leakages = DataLeakage.objects.filter(
            sensitivity__gte=threshold
        ).select_related('infection_record__asset')
        
        risk_assets = {}
        for leakage in high_risk_leakages:
            asset = leakage.infection_record.asset
            if asset.id not in risk_assets:
                risk_assets[asset.id] = {
                    'asset': {
                        'id': asset.id,
                        'name': asset.name,
                        'ip_address': asset.ip_address,
                        'department': asset.department
                    },
                    'leakages': [],
                    'total_data_loss': 0
                }
            
            risk_assets[asset.id]['leakages'].append({
                'type': leakage.get_leakage_type_display(),
                'sensitivity': leakage.sensitivity,
                'file_count': leakage.file_count,
                'data_size': DataFormatter.format_bytes(leakage.data_size)
            })
            risk_assets[asset.id]['total_data_loss'] += leakage.data_size
        
        # 转换为列表并格式化总数据丢失量
        result = list(risk_assets.values())
        for item in result:
            item['total_data_loss'] = DataFormatter.format_bytes(
                item['total_data_loss']
            )
        
        return result 