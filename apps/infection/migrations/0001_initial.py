# Generated by Django 5.1.3 on 2024-11-21 08:50

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('assets', '0001_initial'),
        ('exercise', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='InfectionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('status', models.CharField(choices=[('IN', '已感染'), ('EN', '已加密'), ('DE', '已解密'), ('CL', '已清理'), ('FA', '感染失败')], default='IN', max_length=2, verbose_name='感染状态')),
                ('infection_time', models.DateTimeField(auto_now_add=True, verbose_name='感染时间')),
                ('encrypted_files', models.IntegerField(default=0, verbose_name='加密文件数')),
                ('data_loss', models.BigIntegerField(default=0, verbose_name='数据丢失量(字节)')),
                ('ransom_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='勒索金额')),
                ('description', models.TextField(blank=True, verbose_name='详细描述')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='infection_records', to='assets.asset', verbose_name='感染资产')),
                ('exercise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='infection_records', to='exercise.exercise', verbose_name='所属演练')),
            ],
            options={
                'verbose_name': '感染记录',
                'verbose_name_plural': '感染记录',
                'ordering': ['-infection_time'],
            },
        ),
        migrations.CreateModel(
            name='DataLeakage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('leakage_type', models.CharField(choices=[('DOC', '文档文件'), ('DB', '数据库文件'), ('SRC', '源代码'), ('CFG', '配置文件'), ('CRE', '凭证信息'), ('OTH', '其他')], max_length=3, verbose_name='泄露类型')),
                ('file_count', models.IntegerField(verbose_name='文件数量')),
                ('data_size', models.BigIntegerField(verbose_name='数据大小(字节)')),
                ('sensitivity', models.IntegerField(help_text='1-10的敏感度评分，10为最敏感', verbose_name='敏感度')),
                ('file_types', models.JSONField(help_text='各类型文件的数量统计，如{"doc": 5, "pdf": 3}', verbose_name='文件类型统计')),
                ('detected_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='检测时间')),
                ('description', models.TextField(blank=True, verbose_name='详细描述')),
                ('infection_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='data_leakages', to='infection.infectionrecord', verbose_name='关联感染记录')),
            ],
            options={
                'verbose_name': '数据泄露记录',
                'verbose_name_plural': '数据泄露记录',
                'ordering': ['-detected_time'],
            },
        ),
        migrations.CreateModel(
            name='PhishingRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('ip_address', models.GenericIPAddressField(verbose_name='来源IP')),
                ('click_time', models.DateTimeField(auto_now_add=True, verbose_name='点击时间')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('referrer', models.URLField(blank=True, verbose_name='来源URL')),
                ('email_template', models.CharField(max_length=100, verbose_name='邮件模板')),
                ('is_infected', models.BooleanField(default=False, verbose_name='是否成功感染')),
                ('exercise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='phishing_records', to='exercise.exercise', verbose_name='所属演练')),
            ],
            options={
                'verbose_name': '钓鱼记录',
                'verbose_name_plural': '钓鱼记录',
                'ordering': ['-click_time'],
            },
        ),
    ]
