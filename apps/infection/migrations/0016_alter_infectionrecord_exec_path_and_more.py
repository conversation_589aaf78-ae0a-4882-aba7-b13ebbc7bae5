# Generated by Django 5.1.3 on 2025-02-17 09:30

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('infection', '0015_alter_device_device_id'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='infectionrecord',
            name='exec_path',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='执行路径'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='location',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='位置信息'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='system_time',
            field=models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='系统时间'),
        ),
        migrations.AlterField(
            model_name='infectionrecord',
            name='system_version',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='系统版本'),
        ),
    ]
