# Generated by Django 5.1.3 on 2024-12-21 15:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('infection', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='infectionrecord',
            options={'ordering': ['-id'], 'verbose_name': '感染记录', 'verbose_name_plural': '感染记录'},
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='client_id',
            field=models.CharField(blank=True, max_length=36, verbose_name='客户端ID'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='exec_path',
            field=models.CharField(blank=True, max_length=1024, verbose_name='执行路径'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='hostname',
            field=models.CharField(blank=True, max_length=255, verbose_name='主机名'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='location',
            field=models.CharField(blank=True, max_length=255, verbose_name='位置信息'),
        ),
        migrations.AddField(
            model_name='infectionrecord',
            name='username',
            field=models.CharField(blank=True, max_length=255, verbose_name='用户名'),
        ),
    ]
