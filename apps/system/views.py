from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter
from .models import SystemConfig, NotifyModel
from .serializers import (
    SystemConfigSerializer,
    SystemSettingsSerializer, NotificationsSerializers
)
import os
import time
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from extensions import constants


@extend_schema_view(
    list=extend_schema(
        summary="获取系统配置列表",
        description="获取所有系统配置项",
        tags=["系统管理"],
        parameters=[
            OpenApiParameter(
                name='type',
                description='配置类型(SE:安全/NO:通知/BA:备份/OT:其他)',
                type=str,
                required=False
            ),
            OpenApiParameter(
                name='is_active',
                description='是否启用',
                type=bool,
                required=False
            )
        ]
    ),
    create=extend_schema(
        summary="创建系统配置",
        description="创建新的系统配置项",
        tags=["系统管理"]
    ),
    retrieve=extend_schema(
        summary="获取配置详情",
        description="获取指定系统配置的详细信息",
        tags=["系统管理"]
    ),
    update=extend_schema(
        summary="更新系统配置",
        description="更新指定系统配置的信息",
        tags=["系统管理"]
    ),
    destroy=extend_schema(
        summary="删除系统配置",
        description="删除指定系统配置",
        tags=["系统管理"]
    ),
    partial_update=extend_schema(
        summary="更新系统配置",
        description="更新指定系统配置的信息",
        tags=["系统管理"]
    )
)
class SystemConfigViewSet(viewsets.ModelViewSet):
    """系统配置管理视图集"""
    queryset = SystemConfig.objects.all()
    serializer_class = SystemConfigSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description']

    def get_queryset(self):
        """支持过滤查询"""
        queryset = super().get_queryset()
        
        # 配置类型过滤
        config_type = self.request.query_params.get('type')
        if config_type:
            queryset = queryset.filter(config_type=config_type)
            
        # 是否启用过滤
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)
            
        return queryset

    def perform_create(self, serializer):
        """创建时记录创建者"""
        serializer.save(created_by=self.request.user)

@extend_schema_view(
    list=extend_schema(
        summary="获取系统设置",
        description="获取所有系统设置配置",
        tags=["系统管理"]
    ),
    update=extend_schema(
        summary="更新系统设置",
        description="更新系统设置配置",
        tags=["系统管理"]
    ),
    partial_update=extend_schema(
        summary="更新系统设置",
        description="更新系统设置配置",
        tags=["系统管理"]
    ),
    destroy=extend_schema(
        summary="删除系统设置",
        description="删除系统设置配置",
        tags=["系统管理"]
    ),
    create=extend_schema(
        summary="创建系统设置",
        description="创建系统设置配置",
        tags=["系统管理"]
    )
)
class SystemSettingsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = SystemSettingsSerializer
    
    def get_permissions(self):
        """
        根据不同的操作返回不同的权限
        """
        if self.action == 'update':
            permission_classes = [IsAuthenticated, IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def list(self, request):
        """获取系统设置"""
        try:
            settings = {
                'systemName': self.get_config_value('system_name', '勒索病毒演练平台'),
                'logo': self.get_config_value('system_logo', ''),
                'copyright': self.get_config_value('system_copyright', '© 2024 All Rights Reserved'),
                'timezone': self.get_config_value('timezone', 'UTC+8'),
                'dateFormat': self.get_config_value('date_format', 'YYYY-MM-DD'),
                'passwordPolicy': {
                    'minLength': int(self.get_config_value('password_min_length', '8')),
                    'requireUppercase': self.get_config_value('password_require_uppercase', 'true') == 'true',
                    'requireNumbers': self.get_config_value('password_require_numbers', 'true') == 'true',
                    'requireSpecialChars': self.get_config_value('password_require_special', 'false') == 'true'
                },
                'sessionTimeout': int(self.get_config_value('session_timeout', '30')),
                'maxLoginAttempts': int(self.get_config_value('max_login_attempts', '5')),
                'notifications': {
                    'newInfection': self.get_config_value('notify_new_infection', 'true') == 'true',
                    'systemUpdate': self.get_config_value('notify_system_update', 'true') == 'true',
                    'backupComplete': self.get_config_value('notify_backup_complete', 'true') == 'true'
                }
            }
            return Response(settings)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create(self, request):
        """不允许创建新的系统设置"""
        return Response(
            {'error': '不支持创建新的系统设置'},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    def update(self, request, pk=None):
        """更新系统设置"""
        serializer = self.serializer_class(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        data = serializer.validated_data
        try:
            # 更新系统设置，使用 name 而不是 key
            self.set_config_value('system_name', data['systemName'])
            self.set_config_value('system_logo', data['logo'])
            self.set_config_value('system_copyright', data['copyright'])
            self.set_config_value('timezone', data['timezone'])
            self.set_config_value('date_format', data['dateFormat'])
            
            # 更新密码策略
            self.set_config_value('password_min_length', str(data['passwordPolicy']['minLength']))
            self.set_config_value('password_require_uppercase', str(data['passwordPolicy']['requireUppercase']).lower())
            self.set_config_value('password_require_numbers', str(data['passwordPolicy']['requireNumbers']).lower())
            self.set_config_value('password_require_special', str(data['passwordPolicy']['requireSpecialChars']).lower())
            
            # 更新会话设置
            self.set_config_value('session_timeout', str(data['sessionTimeout']))
            self.set_config_value('max_login_attempts', str(data['maxLoginAttempts']))
            
            # 更新通知设置
            self.set_config_value('notify_new_infection', str(data['notifications']['newInfection']).lower())
            self.set_config_value('notify_system_update', str(data['notifications']['systemUpdate']).lower())
            self.set_config_value('notify_backup_complete', str(data['notifications']['backupComplete']).lower())

            return Response({'message': '系统设置更新成功'})
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def partial_update(self, request, pk=None):
        """不允许部分更新"""
        return Response(
            {'error': '不支持部分更新系统设置'},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    def destroy(self, request, pk=None):
        """不允许删除系统设置"""
        return Response(
            {'error': '不支持删除系统设置'},
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )

    def get_config_value(self, name: str, default: str = '') -> str:
        """获取配置值"""
        try:
            config = SystemConfig.objects.get(name=name)
            value = config.value.get('value', default) if isinstance(config.value, dict) else default
            
            # 如果是 logo 字段且不是完整 URL，则添加域名
            if name == 'system_logo' and value:
                if not value.startswith(('http://', 'https://')):
                    protocol = 'https' if self.request.is_secure() else 'http'
                    domain = self.request.get_host()
                    value = f'{protocol}://{domain}{value}'
                else:
                    # 如果是OSS URL,去掉签名参数
                    from urllib.parse import urlparse, parse_qs
                    parsed_url = urlparse(value)
                    # 只保留路径部分,去掉查询参数
                    value = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
            
            return value
        except SystemConfig.DoesNotExist:
            return default

    def set_config_value(self, name: str, value: str):
        """设置配置值"""
        config, _ = SystemConfig.objects.update_or_create(
            name=name,
            defaults={
                'value': {'value': value},
                'config_type': 'OT',  # 默认为其他类型
                'created_by': self.request.user
            }
        )
        return config

    @extend_schema(
        summary="上传系统Logo",
        description="上传新的系统Logo图片",
        tags=["系统管理"],
        request={
            'multipart/form-data': {
                'type': 'object',
                'properties': {
                    'logo': {'type': 'string', 'format': 'binary'}
                }
            }
        },
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'logo_url': {'type': 'string', 'description': 'Logo的URL地址'}
                }
            }
        }
    )
    @action(detail=False, methods=['post'])
    def upload_logo(self, request):
        """上传系统Logo"""
        if 'logo' not in request.FILES:
            return Response(
                {'error': '请选择要上传的Logo文件'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        logo_file = request.FILES['logo']
        
        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/png', 'image/gif']
        if logo_file.content_type not in allowed_types:
            return Response(
                {'error': '只支持 JPG、PNG、GIF 格式的图片'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # 验证文件大小（例如最大 2MB）
        if logo_file.size > 2 * 1024 * 1024:
            return Response(
                {'error': 'Logo文件大小不能超过2MB'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # 生成文件名（使用时间戳避免重名）
        file_ext = os.path.splitext(logo_file.name)[1]
        filename = f'system/logo/logo_{int(time.time())}{file_ext}'
        
        # 使用默认存储后端保存文件
        from django.core.files.storage import default_storage
        file_path = default_storage.save(filename, logo_file)
        
        # 获取文件的URL
        logo_url = default_storage.url(file_path)
        
        # 更新系统设置
        self.set_config_value('system_logo', logo_url)
        
        return Response({'logo_url': logo_url})


@extend_schema_view(
    create=extend_schema(
        summary="上传文件",
        description="上传文件到指定路径",
        tags=["系统管理"],
        request={
            'multipart/form-data': {
                'type': 'object',
                'properties': {
                    'file': {'type': 'string', 'format': 'binary', 'description': '要上传的文件'},
                    'path': {'type': 'string', 'description': '上传路径类型，用于确定文件保存位置'}
                },
                'required': ['file', 'path']
            }
        },
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'url': {'type': 'string', 'description': '文件访问URL'},
                    'file_path': {'type': 'string', 'description': '文件存储路径'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'detail': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
)
class UploadViewSet(viewsets.ViewSet):
    permission_classes = []
    serializer_class = SystemSettingsSerializer

    def create(self, request, *args, **kwargs):
        file = request.FILES['file']
        path = request.data.get("path")
        upload_path = constants.UPLOAD_FILE_PATH_DIC.get(path)
        if not upload_path:
            return Response({"detail": "上传文件路径错误"}, status=status.HTTP_400_BAD_REQUEST)
        # file_ext = os.path.splitext(file.name)
        filename = f'{upload_path}/{file.name}'

        from django.core.files.storage import default_storage
        file_path = default_storage.save(filename, file)

        # 获取文件的URL
        url = default_storage.url(file_path)

        return Response({'url': url,
                         "file_path": file_path})


class Notifications(viewsets.ModelViewSet):
    """系统通知消息"""
    queryset = NotifyModel.objects.all()
    serializer_class = NotificationsSerializers
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user).order_by("-id")

    @action(detail=True, methods=['patch'])
    def read(self, request, pk=None):
        obj = self.get_object()
        obj.is_read = True
        obj.save()
        return Response({"msg": "更新成功"})

    @action(detail=False, methods=['post'])
    def read_all(self, request, pk=None):
        user = self.request.user
        NotifyModel.objects.filter(user=user).update(is_read=True)
        return Response({"msg": "更新成功"})

    @action(detail=False, methods=['post'])
    def unread_count(self, request, pk=None):
        user = self.request.user
        count = NotifyModel.objects.filter(user=user, is_read=False).count()
        return Response({"count": count})
