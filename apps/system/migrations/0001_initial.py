# Generated by Django 5.1.3 on 2025-01-07 16:02

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='配置名称')),
                ('smtp_host', models.CharField(max_length=255, verbose_name='SMTP服务器')),
                ('smtp_port', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(65535)], verbose_name='SMTP端口')),
                ('smtp_username', models.CharField(max_length=255, verbose_name='SMTP用户名')),
                ('smtp_password', models.CharField(max_length=255, verbose_name='SMTP密码')),
                ('use_tls', models.BooleanField(default=True, verbose_name='使用TLS')),
                ('use_ssl', models.BooleanField(default=False, verbose_name='使用SSL')),
                ('default_from_email', models.EmailField(max_length=255, verbose_name='默认发件人')),
                ('timeout', models.IntegerField(default=10, validators=[django.core.validators.MinValueValidator(1)], verbose_name='超时时间(秒)')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('description', models.TextField(blank=True, verbose_name='配置说明')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '邮件配置',
                'verbose_name_plural': '邮件配置',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='配置名称')),
                ('value', models.JSONField(default=dict, verbose_name='配置值')),
                ('config_type', models.CharField(choices=[('EM', '邮件配置'), ('SE', '安全配置'), ('NO', '通知配置'), ('BA', '备份配置'), ('OT', '其他配置')], default='OT', max_length=2, verbose_name='配置类型')),
                ('description', models.TextField(blank=True, verbose_name='配置说明')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
                'ordering': ['name'],
            },
        ),
    ]
