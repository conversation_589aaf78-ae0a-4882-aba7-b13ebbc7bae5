from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter
from .models import User, SMSLog
from .serializers import (
    UserSerializer,
    UserCreateSerializer,
    UserProfileSerializer,
    SMSLogSerializer,
    ResetPasswordSerializer,
    ChangePasswordSerializer,
    PhoneLoginSerializer,
    SendVerificationCodeSerializer,
)
from django.core.cache import cache
from django.conf import settings
import random
import logging
from alibabacloud_dysmsapi20170525.client import Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
import json


@extend_schema_view(
    list=extend_schema(
        summary="获取用户列表",
        description="获取所有用户信息列表",
        tags=["用户管理"],
        parameters=[
            OpenApiParameter(
                name="department", description="部门ID", type=int, required=False
            ),
            OpenApiParameter(
                name="role",
                description="角色(superadmin/admin/user)",
                type=str,
                required=False,
            ),
            OpenApiParameter(
                name="is_active", description="是否激活", type=bool, required=False
            ),
            OpenApiParameter(
                name="search",
                description="搜索关键词(用户名/邮箱/手机)",
                type=str,
                required=False,
            ),
        ],
        responses={200: UserSerializer(many=True)},
    ),
    create=extend_schema(
        summary="创建用户",
        description="创建新用户",
        tags=["用户管理"],
        request=UserCreateSerializer,
        responses={
            201: UserSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}},
        },
    ),
    retrieve=extend_schema(
        summary="获取用户详情",
        description="获取指定用户的详细信息",
        tags=["用户管理"],
        responses={
            200: UserSerializer,
            404: {"type": "object", "properties": {"error": {"type": "string"}}},
        },
    ),
    update=extend_schema(
        summary="更新用户",
        description="更新指定用户的信息",
        tags=["用户管理"],
        request=UserSerializer,
        responses={
            200: UserSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}},
        },
    ),
    destroy=extend_schema(
        summary="删除用户",
        description="删除指定用户",
        tags=["用户管理"],
        responses={
            204: None,
            404: {"type": "object", "properties": {"error": {"type": "string"}}},
        },
    ),
    partial_update=extend_schema(
        summary="部分更新用户",
        description="部分更新指定用户的信息",
        tags=["用户管理"],
        request=UserSerializer,
        responses={
            200: UserSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}},
            404: {"type": "object", "properties": {"error": {"type": "string"}}},
        },
    ),
)
class UserViewSet(viewsets.ModelViewSet):
    """用户管理视图集"""

    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]
    filter_backends = [filters.SearchFilter]
    search_fields = ["username", "email", "phone"]

    def get_queryset(self):
        """
        重写get_queryset方法以支持过滤
        """
        queryset = User.objects.all()
        
        # 部门过滤
        department = self.request.query_params.get('department')
        if department:
            queryset = queryset.filter(department=department)
            
        # 角色过滤
        role = self.request.query_params.get('role')
        if role:
            queryset = queryset.filter(role=role)
            
        # 是否激活过滤
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)
            
        # 搜索过滤（已通过filter_backends实现）
            
        return queryset.order_by("-id")

    def get_serializer_class(self):
        if self.action == "create":
            return UserCreateSerializer
        return UserSerializer

    @extend_schema(
        summary="修改密码",
        description="修改当前用户的密码",
        tags=["用户管理"],
        request=ChangePasswordSerializer,
        responses={
            200: {"type": "object", "properties": {"message": {"type": "string"}}}
        },
    )
    @action(detail=False, methods=["post"])
    def change_password(self, request):
        """修改密码"""
        old_password = request.data.get("old_password")
        new_password = request.data.get("new_password")

        if not all([old_password, new_password]):
            return Response(
                {"error": "请提供旧密码和新密码"}, status=status.HTTP_400_BAD_REQUEST
            )

        if not request.user.check_password(old_password):
            return Response({"error": "旧密码错误"}, status=status.HTTP_400_BAD_REQUEST)

        request.user.set_password(new_password)
        request.user.save()

        return Response({"message": "密码修改成功"})

    @extend_schema(
        summary="重置用户密码",
        description="管理员重置指定用户的密码",
        tags=["用户管理"],
        request=ResetPasswordSerializer,
        responses={
            200: {"type": "object", "properties": {"message": {"type": "string"}}}
        },
    )
    @action(detail=True, methods=["post"])
    def reset_password(self, request, pk=None):
        """重置用户密码"""
        if not request.user.is_superadmin:
            return Response(
                {"error": "只有超级管理员可以重置密码"},
                status=status.HTTP_403_FORBIDDEN,
            )

        new_password = request.data.get("new_password")
        if not new_password:
            return Response(
                {"error": "请提供新密码"}, status=status.HTTP_400_BAD_REQUEST
            )

        user = self.get_object()
        user.set_password(new_password)
        user.save()

        return Response({"message": "密码重置成功"})

    @extend_schema(
        summary="更新头像",
        description="更新当前用户的头像",
        tags=["用户管理"],
        request={
            "multipart/form-data": {
                "type": "object",
                "properties": {"avatar": {"type": "string", "format": "binary"}},
            }
        },
        responses={200: UserProfileSerializer},
    )
    @action(
        detail=False, methods=["post"], permission_classes=[permissions.IsAuthenticated]
    )
    def update_avatar(self, request):
        """更新用户头像"""
        try:
            avatar = request.FILES.get("avatar")
            if not avatar:
                return Response(
                    {"error": "请上传头像文件"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 验证文件类型
            allowed_types = ["image/jpeg", "image/png", "image/gif"]
            if avatar.content_type not in allowed_types:
                return Response(
                    {"error": "不支持的文件类型，请上传JPG、PNG或GIF格式的图片"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # 验证文件大小（限制为2MB）
            if avatar.size > 2 * 1024 * 1024:
                return Response(
                    {"error": "文件大小不能超过2MB"}, status=status.HTTP_400_BAD_REQUEST
                )

            # 删除旧头像
            if request.user.avatar:
                request.user.avatar.delete(save=False)

            # 保存新头像
            request.user.avatar = avatar
            request.user.save()

            serializer = UserProfileSerializer(
                request.user, context={"request": request}
            )
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _create_sms_client(self):
        """创建阿里云短信客户端"""
        config = open_api_models.Config(
            access_key_id=settings.ALIYUN_SMS["ACCESS_KEY_ID"],
            access_key_secret=settings.ALIYUN_SMS["ACCESS_KEY_SECRET"],
            region_id=settings.ALIYUN_SMS["REGION_ID"],
        )
        config.endpoint = "dysmsapi.aliyuncs.com"
        return Client(config)

    @extend_schema(
        summary="发送手机验证码",
        description="向指定手机号发送登录验证码",
        tags=["用户管理"],
        request=SendVerificationCodeSerializer,
        responses={
            200: {"type": "object", "properties": {"message": {"type": "string"}}}
        },
    )
    @action(detail=False, methods=["post"], permission_classes=[permissions.AllowAny])
    def send_verification_code(self, request):
        """发送手机验证码"""
        phone = request.data.get("phone")
        if not phone:
            return Response(
                {"error": "请提供手机号码"}, status=status.HTTP_400_BAD_REQUEST
            )

        # 检查发送频率限制
        cache_key_limit = f"sms_limit_{phone}"
        if cache.get(cache_key_limit):
            return Response(
                {"error": "发送太频繁，请稍后再试"}, status=status.HTTP_400_BAD_REQUEST
            )

        # 生成6位随机验证码
        verification_code = "".join([str(random.randint(0, 9)) for _ in range(6)])

        try:
            client = self._create_sms_client()

            # 构建短信发送请求
            send_request = dysmsapi_models.SendSmsRequest(
                phone_numbers=phone,
                sign_name=settings.ALIYUN_SMS["SIGN_NAME"],
                template_code=settings.ALIYUN_SMS["TEMPLATE_CODE"],
                template_param=json.dumps({"code": verification_code}),
            )

            # 发送短信
            response = client.send_sms(send_request)

            # 创建短信记录
            sms_log = SMSLog.objects.create(
                phone=phone,
                code=verification_code,
                template_code=settings.ALIYUN_SMS["TEMPLATE_CODE"],
                status=response.body.code,
                message=response.body.message,
                request_id=response.body.request_id,
                biz_id=response.body.biz_id,
            )

            if response.body.code == "OK":
                # 将验证码保存到缓存中，设置5分钟过期
                cache_key = f"phone_verification_{phone}"
                cache.set(cache_key, verification_code, 300)

                # 设置发送频率限制（60秒内不能重复发送）
                cache.set(cache_key_limit, True, 60)

                return Response({"message": "验证码已发送"})
            else:
                logging.error(f"发送验证码失败: {response.body.message}")
                return Response(
                    {"error": "发送验证码失败"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            logging.error(f"发送验证码失败: {str(e)}")
            # 记录发送异常
            SMSLog.objects.create(
                phone=phone,
                code=verification_code,
                template_code=settings.ALIYUN_SMS["TEMPLATE_CODE"],
                status="ERROR",
                message=str(e),
            )
            return Response(
                {"error": "发送验证码失败"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @extend_schema(
        summary="手机验证码登录",
        description="使用手机号和验证码进行登录",
        tags=["用户管理"],
        request=PhoneLoginSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {"refresh": {"type": "string"}, "access": {"type": "string"}},
            }
        },
    )
    @action(detail=False, methods=["post"], permission_classes=[permissions.AllowAny])
    def phone_login(self, request):
        """手机验证码登录"""
        phone = request.data.get("phone")
        verification_code = request.data.get("verification_code")

        if not all([phone, verification_code]):
            return Response(
                {"error": "请提供手机号和验证码"}, status=status.HTTP_400_BAD_REQUEST
            )

        # 验证验证码
        cache_key = f"phone_verification_{phone}"
        cached_code = cache.get(cache_key)

        if not cached_code or cached_code != verification_code:
            return Response(
                {"error": "验证码错误或已过期"}, status=status.HTTP_400_BAD_REQUEST
            )

        # 查找用户
        try:
            user = User.objects.get(phone=phone)
        except User.DoesNotExist:
            return Response(
                {"error": "该手机号未注册，请先注册账号"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # 检查用户状态
        if not user.is_active:
            return Response(
                {"error": "该账号已被禁用，请联系管理员"}, 
                status=status.HTTP_403_FORBIDDEN
            )

        # 生成token
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(user)

        # 清除验证码缓存
        cache.delete(cache_key)

        return Response({
            "refresh": str(refresh),
            "access": str(refresh.access_token)
        })

    @extend_schema(
        summary="获取短信发送记录",
        description="获取短信发送记录列表（仅管理员可用）",
        tags=["用户管理"],
        parameters=[
            OpenApiParameter(
                name="phone", description="手机号码", type=str, required=False
            ),
            OpenApiParameter(
                name="status", description="发送状态", type=str, required=False
            ),
        ],
        responses={200: SMSLogSerializer(many=True)},
    )
    @action(detail=False, methods=["get"], permission_classes=[permissions.IsAdminUser])
    def sms_logs(self, request):
        """获取短信发送记录"""
        phone = request.query_params.get("phone")
        status = request.query_params.get("status")

        queryset = SMSLog.objects.all()
        if phone:
            queryset = queryset.filter(phone=phone)
        if status:
            queryset = queryset.filter(status=status)

        serializer = SMSLogSerializer(queryset, many=True)
        return Response(serializer.data)
