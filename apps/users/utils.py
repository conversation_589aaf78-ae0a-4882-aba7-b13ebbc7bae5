from rest_framework.views import exception_handler
from rest_framework.exceptions import APIException
from django.utils.translation import gettext as _

def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)
    
    if response is not None:
        # 自定义错误消息映射
        error_messages = {
            'No active account found with the given credentials': '用户名或密码错误',
            'Invalid token': '无效的令牌',
            'Token has expired': '令牌已过期',
            'Authentication credentials were not provided': '未提供认证凭据',
            'User account is disabled': '用户账号已被禁用',
            'Given token not valid for any token type': '令牌已过期,请重新登录',
            'Invalid verification code': '验证码错误',
            'User not found': '用户不存在',
            'Invalid phone number': '无效的手机号码',
            'Invalid verification code': '验证码错误',
            'User not found': '用户不存在',
        }
        
        # 获取原始错误消息
        if isinstance(response.data, dict):
            original_detail = str(response.data.get('detail', ''))
        else:
            original_detail = str(response.data)
            
        # 替换为中文错误消息
        if original_detail in error_messages:
            if isinstance(response.data, dict):
                response.data['detail'] = error_messages[original_detail]
            else:
                response.data = {'detail': error_messages[original_detail]}
    
    return response
