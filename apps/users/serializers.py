from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from .models import SMSLog
from drf_spectacular.utils import extend_schema_field
from django.urls import reverse
from rest_framework_simplejwt.tokens import RefreshToken
from urllib.parse import quote

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    last_login = serializers.SerializerMethodField()
    date_joined = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email',
            'phone', 'avatar',
            'is_active', 'last_login', 'date_joined',
            'is_staff', 'is_superuser', 'avatar'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def get_last_login(self, obj):
        if obj.last_login:
            return obj.last_login.strftime('%Y-%m-%d %H:%M:%S')
        return None

    def get_date_joined(self, obj):
        if obj.date_joined:
            return obj.date_joined.strftime('%Y-%m-%d %H:%M:%S')
        return None


class UserCreateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])

    class Meta:
        model = User
        fields = [
            'username', 'password', 'email',
            'phone', 'is_active'
        ]

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)

        refresh = RefreshToken.for_user(user)

        user.tokens = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }

        return user

    def to_representation(self, instance):
        if hasattr(instance, 'tokens'):
            return instance.tokens
        return super().to_representation(instance)


class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['email', 'phone', 'is_active']


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password2 = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password2']:
            raise serializers.ValidationError({"new_password": "两次密码不一致"})
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email',
            'phone', 'avatar',
            'created_at', 'last_login'
        ]
        read_only_fields = ['id', 'username', 'created_at', 'last_login']


class SMSLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = SMSLog
        fields = ['id', 'phone', 'status', 'message', 'created_at']


class ResetPasswordSerializer(serializers.Serializer):
    new_password = serializers.CharField(required=True)


class PhoneLoginSerializer(serializers.Serializer):
    phone = serializers.CharField(required=True)
    verification_code = serializers.CharField(required=True)


class SendVerificationCodeSerializer(serializers.Serializer):
    phone = serializers.CharField(required=True)


class UpdateAvatarSerializer(serializers.Serializer):
    avatar = serializers.ImageField(required=True)
