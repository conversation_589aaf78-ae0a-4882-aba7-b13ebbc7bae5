from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import ExerciseViewSet, ExerciseDashboardStatisticsViewSet, ExercisesTrendViewSet, AssetGroupStatisticsView

# 创建路由器
router = DefaultRouter()
router.register(r'exercises', ExerciseViewSet, basename='exercise')
# 数据看板-演练数据
router.register(r'exercises_dashboard_statistics', ExerciseDashboardStatisticsViewSet,
                basename='exercises_dashboard_statistics')

# 演练趋势
router.register(r'exercises_trend', ExercisesTrendViewSet,
                basename='exercises_trend')

# 资产分布
router.register(r'group_statistics', AssetGroupStatisticsView,
                basename='group_statistics')
# 定义URL模式
urlpatterns = [
    path('', include(router.urls)),
]
