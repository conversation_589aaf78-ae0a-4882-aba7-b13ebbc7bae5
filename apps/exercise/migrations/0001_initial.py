# Generated by Django 5.1.3 on 2024-11-21 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Exercise',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(help_text='演练项目的名称，用于标识不同的演练', max_length=100, verbose_name='演练名称')),
                ('description', models.TextField(blank=True, help_text='详细描述演练的目的、范围和预期结果', verbose_name='演练描述')),
                ('start_time', models.DateTimeField(help_text='演练计划开始的时间', verbose_name='开始时间')),
                ('end_time', models.DateTimeField(help_text='演练计划结束的时间', verbose_name='结束时间')),
                ('status', models.CharField(choices=[('PE', '待开始'), ('RU', '进行中'), ('PA', '已暂停'), ('FI', '已完成'), ('TE', '已终止')], default='PE', help_text='当前演练的执行状态', max_length=2, verbose_name='状态')),
            ],
            options={
                'verbose_name': '演练项目',
                'verbose_name_plural': '演练项目',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExerciseLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('event_type', models.CharField(choices=[('ST', '开始演练'), ('PA', '暂停演练'), ('RE', '恢复演练'), ('FI', '完成演练'), ('TE', '终止演练'), ('IN', '感染事件'), ('ER', '错误事件')], max_length=2, verbose_name='事件类型')),
                ('description', models.TextField(help_text='详细记录事件的具体情况', verbose_name='事件描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='事件发生的具体时间', verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '演练日志',
                'verbose_name_plural': '演练日志',
                'ordering': ['-created_at'],
            },
        ),
    ]
