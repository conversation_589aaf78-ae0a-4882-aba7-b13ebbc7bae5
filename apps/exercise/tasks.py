import os
import uuid

from jinja2 import Template
from django.core.files.storage import default_storage
from django.utils import timezone
from django_q.tasks import async_task, schedule
from django_q.models import Schedule

from extensions import email_utils, constants
from .models import Exercise
from apps.phishing.models import EmailLog


def send_single_email(exercise_id, email, content, email_subject, strategy, email_template_file_list, user_flag):
    """
    发送单个邮件的异步任务
    
    Args:
        exercise_id: 演练ID
        email: 收件人邮箱
        content: 邮件内容
        email_subject: 邮件主题
        strategy: 发送策略
        email_template_file_list: 附件列表
        user_flag: 用户标识
    """
    email_log = None
    try:
        # 获取演练对象
        exercise_obj = Exercise.objects.get(id=exercise_id)
        
        # 创建新的邮件发送记录
        email_log = EmailLog.objects.create(
            email_task=exercise_obj.email_task,
            recipient=email,
            exercise=exercise_obj,
            status='SENDING',
            task_id=f'send_email_to_{email}',
            user_flag=user_flag
        )
        
        # 发送邮件
        flag, detail = email_utils.send_email(
            mail_host=strategy.email_server_address,
            mail_user=strategy.email_server_account,
            mail_pass=strategy.email_server_pwd,
            subject=email_subject,
            content=content + constants.EMAIL_IMAGE_URL % (email, exercise_id),
            receivers=[email],
            ignore_cert_errors=strategy.is_ignore_certificate_errors,
            port=strategy.port,
            email_headers=None,
            oss_files=email_template_file_list,
        )
        
        if flag:
            # 发送成功
            email_log.status = 'SUCCESS'
            email_log.sent_at = timezone.now()
            email_log.save()
            return True, "邮件发送成功"
        else:
            # 发送失败
            email_log.status = 'FAILED'
            email_log.error_message = detail
            email_log.retry_count += 1
            email_log.save()
            
            # 如果重试次数小于3,则重试
            if email_log.retry_count < 3:
                email_log.status = 'RETRY'
                email_log.save()
                # 5分钟后重试
                async_task(
                    'apps.exercise.tasks.send_single_email',
                    exercise_id,
                    email,
                    content,
                    email_subject,
                    strategy,
                    email_template_file_list,
                    user_flag,
                    group=f'exercise_email_{exercise_id}',
                    timeout=300,
                    task_name=f'send_email_to_{email}'
                )
                return False, f"邮件发送失败,将在5分钟后重试: {detail}"
            else:
                return False, f"邮件发送失败,已达到最大重试次数: {detail}"
                
    except Exercise.DoesNotExist:
        return False, "演练不存在"
    except Exception as e:
        # 记录错误
        if email_log:
            email_log.status = 'FAILED'
            email_log.error_message = str(e)
            email_log.save()
        return False, str(e)


def send_exercise_emails(exercise_id):
    """
    发送演练邮件的异步任务
    
    Args:
        exercise_id: 演练ID
    """
    try:
        # 获取演练对象
        exercise_obj = Exercise.objects.get(id=exercise_id)
        
        # 检查演练状态
        if exercise_obj.status != Exercise.Status.PENDING:
            return False, "演练状态不正确"
            
        # 获取演练邮箱列表
        emails = []
        for asset_group in exercise_obj.target_asset.all():
            group_emails = asset_group.asset.exclude(
                email=''
            ).exclude(
                email=None
            ).values_list('email', flat=True)
            emails.extend(list(group_emails))
        
        # 去重邮箱列表
        emails = list(set(emails))
        
        if not emails:
            return False, "没有找到有效的目标邮箱"
            
        # 获取邮件内容和配置
        content = exercise_obj.email_task.email_template.content
        email_subject = exercise_obj.email_task.email_template.email_subject
        strategy = exercise_obj.email_task.strategy
        # 钓鱼表单地址
        phishing_link = exercise_obj.email_task.phishing_link
        # email_template_file = exercise_obj.email_task.email_template.email_template_file.all()
        # email_template_file_list = [f"{default_storage.url(row.email_file.name)}" for row in email_template_file]
        
        # 加密器添加到附件
        # encryptor = exercise_obj.virus.encryptor
        # if encryptor:
        #     email_template_file_list.append(encryptor)

        # 加密器
        email_template_file_list = [constants.ENCRYPTOR_FILE_URL]
        template = Template(content)
        # 并发发送邮件
        for email in emails:
            user_flag = uuid.uuid4().hex
            content = template.render({"url": constants.PHISHING_LINK + f"?id={user_flag}"})
            # 创建异步任务
            async_task(
                'apps.exercise.tasks.send_single_email',
                exercise_id,
                email,
                content,
                email_subject,
                strategy,
                email_template_file_list,
                user_flag,
                group=f'exercise_email_{exercise_id}',
                timeout=300,
                task_name=f'send_email_to_{email}'
            )
        
        # 更新演练状态为进行中
        exercise_obj.status = Exercise.Status.RUNNING
        exercise_obj.save()
        
        # 创建结束演练的定时任务
        task = schedule(
            'apps.exercise.tasks.finish_exercise',
            exercise_id,
            schedule_type=Schedule.ONCE,
            next_run=exercise_obj.end_time,
            name=f'exercise_finish_{exercise_id}'
        )
        
        return True, "演练邮件发送任务已创建"
        
    except Exercise.DoesNotExist:
        return False, "演练不存在"
    except Exception as e:
        return False, str(e)


def finish_exercise(exercise_id):
    """
    自动结束演练的异步任务
    
    Args:
        exercise_id: 演练ID
    """
    try:
        # 获取演练对象
        exercise = Exercise.objects.get(id=exercise_id)
        
        # 检查演练状态
        if exercise.status not in [Exercise.Status.RUNNING, Exercise.Status.PAUSED]:
            return False, "演练状态不正确"
            
        # 更新演练状态为已完成
        exercise.status = Exercise.Status.FINISHED
        exercise.save()
        
        return True, "演练已自动结束"
        
    except Exercise.DoesNotExist:
        return False, "演练不存在"
    except Exception as e:
        return False, str(e) 