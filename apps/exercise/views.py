from django.db.models import Count
from django.shortcuts import get_object_or_404
from django.utils import timezone
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status, mixins, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_q.tasks import async_task, schedule
from django_q.models import Schedule, Task
from rest_framework.viewsets import GenericViewSet

from extensions import permissions_utils, queryset_utils
from .filters import ExerciseFilter
from .models import Exercise
from .serializers import (
    ExerciseSerializer
)
from ..assets.models import Asset, AssetGroup
from apps.infection.models import InfectionRecord, Device
from ..phishing.models import EmailLog
from ..system.models import NotifyModel


@extend_schema_view(
    list=extend_schema(
        summary="获取演练列表",
        description="获取所有演练项目列表，所有已认证用户可访问",
        tags=["演练管理"]
    ),
    create=extend_schema(
        summary="创建演练",
        description="创建新的演练项目，仅管理员可操作",
        tags=["演练管理"]
    ),
    retrieve=extend_schema(
        summary="获取演练详情",
        description="获取指定演练项目的详细信息，所有已认证用户可访问",
        tags=["演练管理"]
    ),
    update=extend_schema(
        summary="更新演练",
        description="更新指定演练项目的信息，仅管理员可操作",
        tags=["演练管理"]
    ),
    partial_update=extend_schema(
        summary="部分更新演练",
        description="部分更新指定演练项目的信息，仅管理员可操作",
        tags=["演练管理"]
    ),
    destroy=extend_schema(
        summary="删除演练",
        description="删除指定演练项目，仅管理员可操作",
        tags=["演练管理"]
    )
)
class ExerciseViewSet(queryset_utils.UserOwnedModelViewSet):
    """演练项目管理视图集"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter

    @extend_schema(
        summary="开始演练",
        description="启动指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'},
                    'message': {'type': 'string', 'description': '提示信息'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def start(self, request, pk=None):
        """开始演练"""
        exercise = self.get_object()
        now = timezone.now()

        # 检查演练状态
        if exercise.status != Exercise.Status.PENDING:
            return Response(
                {'error': '只有待开始状态的演练可以启动'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 检查结束时间
        if exercise.end_time < now:
            return Response(
                {'error': '演练已结束,不能进行演练'},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({
            'status': exercise.get_status_display(),
            'message': f'演练将在 {exercise.start_time} 开始执行'
        })
        # 如果已经有定时任务ID,先取消之前的任务
        # if exercise.task_id:
        #     Schedule.objects.filter(id=exercise.task_id).delete()
        #     exercise.task_id = None
        #     exercise.save()

        # 判断是否需要立即开始还是定时执行
        # if exercise.start_time <= now:
        #     # 立即执行
        #     task = async_task(
        #         'apps.exercise.tasks.send_exercise_emails',
        #         exercise.id,
        #         name=f'exercise_send_email_{exercise.id}'
        #     )
        #     exercise.task_id = task
        #     exercise.save()
        #     return Response({
        #         'status': exercise.get_status_display(),
        #         'message': '演练任务已开始执行'
        #     })
        # else:
        #     # 创建定时任务
        #     task = schedule(
        #         'apps.exercise.tasks.send_exercise_emails',
        #         exercise.id,
        #         schedule_type=Schedule.ONCE,
        #         next_run=exercise.start_time,
        #         name=f'exercise_send_email_{exercise.id}'
        #     )
        #     exercise.task_id = task.id
        #     exercise.save()
        #     return Response({
        #         'status': exercise.get_status_display(),
        #         'message': f'演练将在 {exercise.start_time} 开始执行'
        #     })

    @extend_schema(
        summary="暂停演练",
        description="暂停指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def pause(self, request, pk=None):
        """暂停演练"""
        exercise = self.get_object()
        if exercise.status != Exercise.Status.RUNNING:
            return Response(
                {'error': '只有进行中的演练可以暂停'},
                status=status.HTTP_400_BAD_REQUEST
            )

        exercise.status = Exercise.Status.PAUSED
        exercise.save()

        return Response({'status': exercise.get_status_display()})

    @extend_schema(
        summary="结束演练",
        description="结束指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def finish(self, request, pk=None):
        """结束演练"""
        exercise = self.get_object()
        if exercise.status not in [Exercise.Status.RUNNING, Exercise.Status.PAUSED]:
            return Response(
                {'error': '只有进行中或已暂停的演练可以完成'},
                status=status.HTTP_400_BAD_REQUEST
            )

        exercise.status = Exercise.Status.FINISHED
        exercise.end_time = timezone.now()
        exercise.save()

        return Response({'status': exercise.get_status_display()})

    @extend_schema(
        summary="终止演练",
        description="强制终止指定的演练项目",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '演练状态'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=True, methods=['post'])
    def terminate(self, request, pk=None):
        """终止演练"""
        exercise = self.get_object()
        if exercise.status in [Exercise.Status.FINISHED, Exercise.Status.TERMINATED]:
            return Response(
                {'error': '已完成或已终止的演练不能终止'},
                status=status.HTTP_400_BAD_REQUEST
            )

        exercise.status = Exercise.Status.TERMINATED
        exercise.end_time = timezone.now()
        exercise.save()

        return Response({'status': exercise.get_status_display()})

    @extend_schema(
        summary="获取演练统计信息",
        description="获取指定演练项目的统计信息,包括目标设备总数、已感染设备数、钓鱼邮件统计等",
        tags=["演练管理"],
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'total_devices': {'type': 'integer', 'description': '目标设备总数'},
                    'infected_devices': {'type': 'integer', 'description': '已感染设备数'},
                    'phishing_rate': {'type': 'number', 'description': '钓鱼邮件点击率'},
                    'total_emails': {'type': 'integer', 'description': '发送邮件总数'},
                    'clicked_emails': {'type': 'integer', 'description': '已点击邮件数'},
                    'timeline': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'event': {'type': 'string', 'description': '事件描述'},
                                'datetime': {'type': 'string', 'description': '事件时间'},
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取演练统计信息"""
        exercise = self.get_object()

        # 获取演练目标资产组中的所有资产
        target_assets = Asset.objects.filter(group__in=exercise.target_groups.all())

        # 获取终端和邮件资产
        devices = target_assets.filter(asset_type__in=['EP', 'SV'])
        emails = target_assets.filter(asset_type='EM')

        # 获取感染记录
        infection_records = InfectionRecord.objects.filter(
            asset__in=devices,
            created_at__range=(exercise.start_time, exercise.end_time or timezone.now())
        )

        # 统计感染设备
        infected_devices = infection_records.values('asset').distinct().count()

        # 统计邮件点击
        clicked_emails = emails.filter(status='clicked').count()

        # 构建时间轴
        timeline = []

        # 添加开始事件
        if exercise.start_time:
            timeline.append({
                'event': '演练开始',
                'datetime': exercise.start_time.isoformat()
            })

        # 添加感染事件
        for record in infection_records.order_by('created_at'):
            timeline.append({
                'event': f'设备 {record.asset.name} 被感染',
                'datetime': record.created_at.isoformat()
            })

        # 添加结束事件
        if exercise.end_time:
            timeline.append({
                'event': '演练结束',
                'datetime': exercise.end_time.isoformat()
            })

        return Response({
            'total_devices': devices.count(),
            'infected_devices': infected_devices,
            'total_emails': emails.count(),
            'clicked_emails': clicked_emails,
            'phishing_rate': round(clicked_emails / emails.count() * 100 if emails.count() > 0 else 0, 2),
            'timeline': timeline
        })

    @action(detail=True, methods=['get'])
    def department(self, request, pk=None):
        exercise = self.get_object()
        target_assets = exercise.target_asset.all()
        department_data = []
        for group in target_assets:
            # 获取该邮件组下所有资产的邮箱地址
            assets_in_group = group.asset.filter(asset_type='EM').all()
            email_count = assets_in_group.filter(email__isnull=False).count()  # 统计有效邮箱
            # 存储邮件组名和邮箱数
            department_data.append({
                'name': group.name,
                'value': email_count,
            })

        # 返回结果
        return Response({
            'exercise_id': exercise.id,
            'department_data': department_data
        })

    @action(detail=True, methods=['get'])
    def device_statistics(self, request, pk=None):
        # 获取当前演练任务对象
        exercise = self.get_object()

        # 获取所有目标资产组
        target_groups = exercise.target_groups.all()

        # 获取所有目标资产组内的所有资产
        assets = Asset.objects.filter(group__in=target_groups)

        # 统计终端设备和服务器设备的数量
        terminal_count = assets.filter(asset_type='EP').count()  # 终端设备（EP）
        server_count = assets.filter(asset_type='SV').count()  # 服务器设备（SV）

        # 返回统计结果
        return Response([
            {"name": "服务器设备", "value": server_count},
            {"name": "终端设备", "value": terminal_count},
        ])

    @action(detail=True, methods=['get'])
    def infection_statistics(self, request, pk=None):
        """
        获取某个演练下，各资产组的感染设备数量
        """
        # 获取演练对象
        exercise = self.get_object()

        # 获取该演练下的所有目标资产组
        target_groups = exercise.target_groups.all()

        # 统计每个资产组下的感染设备数量
        result = []
        for group in target_groups:
            # 获取该资产组下的所有设备
            infected_count = Device.objects.filter(
                exercise=exercise,
                # device_id__in=[str(asset_id) for asset_id in group.asset.values_list('id', flat=True)],
                # infection_count__gt=0  # 过滤掉感染次数为0的设备
            ).count()

            # 组装结果
            result.append({
                "name": group.name,
                "value": infected_count
            })

        return Response(result)

    @action(detail=False, methods=['get'], permission_classes=[], authentication_classes=[])
    def reserve_email(self, request, pk=None):
        """
        接收邮件
        """
        email = request.query_params.get("email")
        exercise_id = request.query_params.get("exercise_id")
        if not (email and exercise_id):
            return Response({"msg": "缺失必要参数"})
        from loguru import logger
        logger.info(f"email,exercise_id============{email},{exercise_id}=====================")
        email_log_objs = EmailLog.objects.filter(recipient=email, exercise_id=exercise_id)
        email_log_objs.update(
            is_click=True
        )
        exercise = get_object_or_404(Exercise, id=exercise_id)
        NotifyModel.objects.create(
            exercise_id=exercise_id,
            user=exercise.created_by,
            content=f"{exercise.name}演练下的{email}打开了邮件"
        )
        return Response({"msg": f"更新成功{email_log_objs.count()}"})

    @action(detail=True, methods=['get'])
    def send_email_records(self, request, pk=None):
        """邮件发送记录"""
        exercise = self.get_object()
        email_logs = EmailLog.objects.filter(exercise=exercise)
        data = []
        for row in email_logs:
            data.append({
                "recipient": row.recipient,
                "email_subject": row.email_task.email_template.email_subject,
                "send_time": row.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "is_click": row.is_click,
            })
        return Response({"results": data})


class ExerciseDashboardStatisticsViewSet(queryset_utils.UserOwnedModelViewSet):
    """数据看板-演练数据"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter

    def list(self, request, *args, **kwargs):
        user = self.request.user
        total_exercises = Exercise.objects.filter(created_by=user).count()

        # 统计进行中的演练数量
        running_exercises = Exercise.objects.filter(status="RU", created_by=user).count()

        # 统计已感染的设备数量
        infected_devices = Device.objects.filter(infection_count__gt=0, exercise__created_by=user).count()

        # 统计总设备数量，防止除零错误
        total_devices = Device.objects.filter(exercise__created_by=user).count()
        infection_rate = (infected_devices / total_devices * 100) if total_devices > 0 else 0

        return Response({
            "total_exercises": total_exercises,
            "running_exercises": running_exercises,
            "infected_devices": infected_devices,
            "infection_rate": round(infection_rate, 2)  # 保留两位小数
        })


class ExercisesTrendViewSet(mixins.ListModelMixin,
                            GenericViewSet):
    """数据看板-场次"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter

    def get_trend_data(self, days):
        """计算过去 `days` 天的演练趋势和时间线"""
        today = timezone.now()
        start_date = today - timezone.timedelta(days=days)

        # 统计总场次
        count = Exercise.objects.filter(start_time__gte=start_date, created_by=self.request.user).count()

        # 统计每日演练数量
        timeline = []
        for i in range(days):
            day = today - timezone.timedelta(days=i)
            daily_count = Exercise.objects.filter(start_time__date=day, created_by=self.request.user).count()
            if daily_count > 0:  # 只记录有数据的天数
                timeline.append({"time": day.strftime("%Y-%m-%d"), "count": daily_count})

        return {"count": count, "timeline": timeline[::-1]}  # 逆序，保证时间从旧到新

    def list(self, request, *args, **kwargs):
        return Response({
            "last_7_days": self.get_trend_data(7),
            "last_14_days": self.get_trend_data(14),
            "last_30_days": self.get_trend_data(30),
        })


class AssetGroupStatisticsView(mixins.ListModelMixin,
                               GenericViewSet):
    """数据看板-资产统计"""
    queryset = Exercise.objects.all()
    serializer_class = ExerciseSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filterset_class = ExerciseFilter


    def list(self, request, *args, **kwargs):
        asset_groups = AssetGroup.objects.all().filter(created_by=self.request.user).annotate(
            total_assets=Count("asset"),
        ).values("id", "name", "total_assets")

        # 计算已感染的设备数
        for group in asset_groups:
            infected_count = Device.objects.filter(
                hostname__in=Asset.objects.filter(
                    group_id=group["id"],
                    created_by=self.request.user
                ).values_list("name", flat=True),
                infection_count__gt=0
            ).count()

            group["infected_assets"] = infected_count

        return Response({"groups": list(asset_groups)})
