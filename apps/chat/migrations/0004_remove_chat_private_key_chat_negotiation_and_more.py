# Generated by Django 5.1.3 on 2024-12-26 15:05

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0003_remove_chat_user_chat_private_key'),
        ('virus', '0012_negotiationmodel_deadline'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='chat',
            name='private_key',
        ),
        migrations.AddField(
            model_name='chat',
            name='negotiation',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chat', to='virus.negotiationmodel', verbose_name='谈判配置模型'),
        ),
        migrations.AlterModelTable(
            name='chat',
            table='ls_chat',
        ),
    ]
