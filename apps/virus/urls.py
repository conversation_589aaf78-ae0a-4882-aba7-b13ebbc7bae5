from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import VirusViewSet, RansomTemplateViewSet
from . import views

router = DefaultRouter()
# 病毒管理
router.register('viruses', VirusViewSet)
# 谈判设置
router.register('negotiation', views.NegotiationModelViewSet, basename="negotiation")
router.register('templates', RansomTemplateViewSet)
router.register(r'virus-upload', views.VirusFileUploadViewSet, basename='virus-upload')

urlpatterns = [
    path('', include(router.urls)),
    path('form', views.VerifyView.as_view()),
]
