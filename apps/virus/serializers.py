from rest_framework import serializers

from extensions import file_utils
from .models import Virus, RansomTemplate
from django.utils import timezone
from django.utils.timezone import template_localtime
from . import models


class ListToStringField(serializers.Field):
    """自定义字段，处理列表和字符串的转换"""

    def to_representation(self, value):
        if value:
            if isinstance(value, str):
                return [x.strip() for x in value.split(',') if x.strip()]
            return value
        return []

    def to_internal_value(self, data):
        if isinstance(data, list):
            return ','.join(str(x) for x in data)
        if isinstance(data, str):
            return data
        return ''


class VirusSerializer(serializers.ModelSerializer):
    # 加密器文件
    encryptor_oss_url = file_utils.NoValidationFileField(source="encryptor", required=False,
                                                         allow_null=True, read_only=True, label="加密器文件oss地址")
    encryptor = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    # 壁纸文件
    wallpaper_oss_url = file_utils.NoValidationFileField(source="wallpaper", required=False,
                                                         allow_null=True, read_only=True, label="壁纸oss地址")
    wallpaper = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    # 攻击行为,用逗号分隔的字符串存储
    attack_behavior = ListToStringField(required=False)

    class Meta:
        model = Virus
        fields = ["id", "name", "suffix", "encryptor", "custom_version_note", "count_affected_files_only",
                  "delete_files_after_encryption", "incubation_period", "outbreak_start_time", "outbreak_end_time",
                  "attack_behavior", "encryption_path", "encryption_files_suffix", "is_automated_infection",
                  "ip_discovery", "exercise_ip_pool", "ransom_note_name", "is_negotiation_feature", "wallpaper",
                  "source_code", "enable_virus_evolution", "encryptor_oss_url",
                  "wallpaper_oss_url", "family", "encryptor_name", "outbreak_start_time", "outbreak_end_time",
                  "encrypto_sign", "encrypto_mode", "encrypto_key", "encrypto_iv"]

    def create(self, validated_data: dict) -> Virus:
        """创建病毒"""
        user = self.context["request"].user
        validated_data["created_by"] = user
        return Virus.objects.create(**validated_data)


class RansomTemplateSerializer(serializers.ModelSerializer):
    created_by = serializers.ReadOnlyField(source='created_by.username')

    class Meta:
        model = RansomTemplate
        fields = '__all__'
        read_only_fields = ('created_by', 'created_at', 'updated_at')


class VirusUploadSerializer(serializers.ModelSerializer):
    """病毒样本上传序列化器"""
    file = serializers.FileField(
        required=True,
        error_messages={
            'required': '请选择要上传的病毒样本文件',
            'invalid': '无效的文件格式'
        }
    )

    class Meta:
        model = Virus
        fields = ('name', 'description', 'virus_type', 'encryption_type', 'file', 'ransom_note_template', 'is_active')
        extra_kwargs = {
            'name': {'required': True, 'error_messages': {'required': '病毒名称不能为空'}},
            'virus_type': {'required': True, 'error_messages': {'required': '请选择病毒类型'}},
            'encryption_type': {'required': True, 'error_messages': {'required': '请选择加密类型'}}
        }

    def validate_file(self, value):
        """验证上传的文件"""
        # 检查文件大小（制为 10MB���
        if value.size > 10 * 1024 * 1024:
            raise serializers.ValidationError('文件大小不能超过10MB')

        # 检查文件扩展名
        allowed_extensions = ['.exe', '.py', '.jar', '.zip']
        ext = value.name.lower().split('.')[-1]
        if f'.{ext}' not in allowed_extensions:
            raise serializers.ValidationError(f'不支持���文件类型，允许的类型：{", ".join(allowed_extensions)}')

        return value


class RansomTemplatePreviewSerializer(serializers.Serializer):
    """勒索信模板预览序列化器"""
    content = serializers.CharField(required=True)
    context = serializers.DictField(required=False)

    def validate_context(self, value):
        """验证上下文数据"""
        required_fields = ['victim_name', 'encrypted_files', 'ransom_amount']
        for field in required_fields:
            if field not in value:
                value[field] = f'[{field}]'  # 使用占位符
        return value


class NegotiationSerializer(serializers.ModelSerializer):
    """
    谈判配置序列化器
    """
    # 公司logo
    company_logo_url = file_utils.NoValidationFileField(source="company_logo", required=False,
                                                        allow_null=True, read_only=True, label="公司logo oss地址")
    company_logo = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    # 会话ID
    chat_id = serializers.IntegerField(source="chat.id", default="", read_only=True)
    # 会话ID
    conversation_id = serializers.CharField(source="chat.conversation_id", default="", read_only=True)

    class Meta:
        model = models.NegotiationModel
        fields = ["id", "n_id", "platform_name", "company_name", "official_website", "index_show_count",
                  "company_introduction", "company_logo", "company_valuation", "stolen_data_volume",
                  "usdt_ransom_amount", "btc_ransom_amount",  "btc_address", "usdt_address", "company_logo_url",
                  "deadline", "chat_id", "conversation_id", "created_at", "updated_at", "enable_auto_reply"]

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user
        instance = super().create(validated_data)
        return instance
