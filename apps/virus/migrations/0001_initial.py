# Generated by Django 5.1.3 on 2024-11-21 08:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RansomTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='模板名称')),
                ('content', models.TextField(verbose_name='勒索信内容')),
                ('language', models.CharField(default='中文', max_length=50, verbose_name='语言')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_templates', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '勒索信模板',
                'verbose_name_plural': '勒索信模板',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Virus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='病毒名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('encryption_type', models.CharField(choices=[('AES', 'AES加密'), ('RSA', 'RSA加密'), ('HYB', '混合加密')], default='AES', max_length=3, verbose_name='加密类型')),
                ('file', models.FileField(upload_to='virus_samples/', verbose_name='病毒样本')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_viruses', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '病毒样本',
                'verbose_name_plural': '病毒样本',
                'ordering': ['-created_at'],
            },
        ),
    ]
