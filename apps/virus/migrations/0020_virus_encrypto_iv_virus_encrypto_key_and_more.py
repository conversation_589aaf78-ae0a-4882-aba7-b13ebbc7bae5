# Generated by Django 5.1.3 on 2025-04-14 16:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("virus", "0019_remove_virus_infection"),
    ]

    operations = [
        migrations.AddField(
            model_name="virus",
            name="encrypto_iv",
            field=models.CharField(blank=True, null=True, verbose_name="对称加密向量"),
        ),
        migrations.AddField(
            model_name="virus",
            name="encrypto_key",
            field=models.CharField(blank=True, null=True, verbose_name="对称加密密钥"),
        ),
        migrations.AddField(
            model_name="virus",
            name="encrypto_mode",
            field=models.IntegerField(
                blank=True,
                choices=[(1, "只改后缀"), (2, "可破解"), (3, "真加密")],
                null=True,
                verbose_name="加密模式",
            ),
        ),
        migrations.AddField(
            model_name="virus",
            name="encrypto_sign",
            field=models.CharField(
                blank=True, null=True, verbose_name="加密文件写入末尾标识"
            ),
        ),
    ]
