# Generated by Django 5.1.3 on 2025-02-12 16:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('family', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='VirusSample',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='样本名称')),
                ('file', models.FileField(upload_to='virus_family_samples/', verbose_name='样本文件')),
                ('virus_family', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='virus_samples', to='family.virusfamily', verbose_name='病毒家族')),
            ],
            options={
                'verbose_name': '病毒样本',
                'verbose_name_plural': '病毒样本',
                'ordering': ['-created_at'],
            },
        ),
    ]
