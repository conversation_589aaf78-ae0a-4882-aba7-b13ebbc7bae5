from rest_framework import serializers
from .models import (
    <PERSON>ir<PERSON>F<PERSON><PERSON>, <PERSON><PERSON>mN<PERSON>, RansomA<PERSON>ress,
    <PERSON>l, Negotiation, Victim, VirusSample
)


class RansomNoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = RansomNote
        exclude = ('virus_family', 'created_at')


class RansomAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = RansomAddress
        exclude = ('virus_family', 'created_at')


class ToolSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    file = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = Tool
        exclude = ('virus_family', 'created_at')

    def get_file_url(self, obj):
        """获取工具文件的完整URL"""
        if obj.file:
            try:
                # 确保返回的URL不包含编码字符
                url = self.context['request'].build_absolute_uri(obj.file.url)
                return url.replace('%252F', '/').replace('%2F', '/')
            except:
                return None
        return None


class NegotiationSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    file = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = Negotiation
        exclude = ('virus_family', 'created_at')

    def get_file_url(self, obj):
        if obj.file:
            return self.context['request'].build_absolute_uri(obj.file.url)
        return None


class VictimSerializer(serializers.ModelSerializer):
    official_website = serializers.URLField(required=True, allow_blank=False)

    class Meta:
        model = Victim
        exclude = ('virus_family', 'created_at')


class VirusSampleSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    file = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = VirusSample
        exclude = ('virus_family', 'created_at')

    def get_file_url(self, obj):
        if obj.file:
            return self.context['request'].build_absolute_uri(obj.file.url)
        return None


class VirusFamilySerializer(serializers.ModelSerializer):
    logo_url = serializers.SerializerMethodField()
    logo = serializers.CharField(required=True)
    ransom_notes = RansomNoteSerializer(many=True, required=False)
    ransom_addresses = RansomAddressSerializer(many=True, required=False)
    tools = ToolSerializer(many=True, required=False)
    negotiations = NegotiationSerializer(many=True, required=False)
    victims = VictimSerializer(many=True, required=False)
    virus_samples = VirusSampleSerializer(many=True, required=False)

    class Meta:
        model = VirusFamily
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def get_logo_url(self, obj):
        """获取logo的完整URL"""
        if obj.logo:
            try:
                # 确保返回的URL不包含编码字符
                url = self.context['request'].build_absolute_uri(obj.logo.url)
                return url.replace('%252F', '/').replace('%2F', '/')
            except:
                return None
        return None

    def _process_file_path(self, file_path):
        """处理文件路径，去除URL参数和编码字符"""
        if not file_path:
            return ''
        try:
            # 如果是完整URL，提取路径部分
            if file_path.startswith('http'):
                from urllib.parse import urlparse, unquote
                parsed = urlparse(unquote(file_path))
                path = parsed.path
                # 移除开头的域名部分
                if path.startswith('/virus_family_logo'):
                    return path[1:]  # 移除开头的斜杠
                return path.split('/', 3)[-1] if len(path.split('/', 3)) > 3 else path
            
            # 处理编码的斜杠
            return unquote(file_path).replace('%2F', '/')
        except:
            return file_path

    def create(self, validated_data):
        # 处理文件路径
        if 'logo' in validated_data:
            validated_data['logo'] = self._process_file_path(validated_data['logo'])

        # 处理嵌套数据
        ransom_notes_data = validated_data.pop('ransom_notes', [])
        ransom_addresses_data = validated_data.pop('ransom_addresses', [])
        tools_data = validated_data.pop('tools', [])
        negotiations_data = validated_data.pop('negotiations', [])
        victims_data = validated_data.pop('victims', [])
        virus_samples_data = validated_data.pop('virus_samples', [])

        # 处理工具和谈判记录的文件路径
        tools_data = [
            {**tool, 'file': self._process_file_path(tool.get('file', ''))}
            for tool in tools_data
        ]
        negotiations_data = [
            {**neg, 'file': self._process_file_path(neg.get('file', ''))}
            for neg in negotiations_data
        ]

        # 创建病毒家族实例
        virus_family = VirusFamily.objects.create(**validated_data)

        # 创建关联数据
        self._create_related_objects(virus_family, {
            'ransom_notes': (RansomNote, ransom_notes_data),
            'ransom_addresses': (RansomAddress, ransom_addresses_data),
            'tools': (Tool, tools_data),
            'negotiations': (Negotiation, negotiations_data),
            'victims': (Victim, victims_data),
            'virus_samples': (VirusSample, virus_samples_data)
        })

        return virus_family

    def update(self, instance, validated_data):
        # 处理文件路径
        if 'logo' in validated_data:
            validated_data['logo'] = self._process_file_path(validated_data['logo'])

        # 处理嵌套数据
        ransom_notes_data = validated_data.pop('ransom_notes', [])
        ransom_addresses_data = validated_data.pop('ransom_addresses', [])
        tools_data = validated_data.pop('tools', [])
        negotiations_data = validated_data.pop('negotiations', [])
        victims_data = validated_data.pop('victims', [])
        virus_samples_data = validated_data.pop('virus_samples', [])

        # 处理工具和谈判记录的文件路径
        tools_data = [
            {**tool, 'file': self._process_file_path(tool.get('file', ''))}
            for tool in tools_data
        ]
        negotiations_data = [
            {**neg, 'file': self._process_file_path(neg.get('file', ''))}
            for neg in negotiations_data
        ]

        # 更新主表数据
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新关联数据
        self._update_related_objects(instance, {
            'ransom_notes': (RansomNote, ransom_notes_data),
            'ransom_addresses': (RansomAddress, ransom_addresses_data),
            'tools': (Tool, tools_data),
            'negotiations': (Negotiation, negotiations_data),
            'victims': (Victim, victims_data),
            'virus_samples': (VirusSample, virus_samples_data)
        })

        return instance

    def _create_related_objects(self, instance, related_data):
        """创建关联对象的通用方法"""
        for field_name, (model_class, data_list) in related_data.items():
            for item_data in data_list:
                model_class.objects.create(virus_family=instance, **item_data)

    def _update_related_objects(self, instance, related_data):
        """更新关联对象的通用方法"""
        for field_name, (model_class, data_list) in related_data.items():
            # 删除旧数据
            getattr(instance, field_name).all().delete()
            # 创建新数据
            for item_data in data_list:
                model_class.objects.create(virus_family=instance, **item_data)


class FileUploadSerializer(serializers.Serializer):
    """文件上传序列化器"""
    file = serializers.FileField(help_text='上传的文件')
