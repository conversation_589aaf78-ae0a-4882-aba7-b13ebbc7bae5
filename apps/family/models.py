from django.db import models
from django.utils.translation import gettext_lazy as _
from config.models import BaseModel


class VirusFamily(BaseModel):
    """病毒家族模型"""

    class EncryptionAlgorithm(models.TextChoices):
        AES = 'AES', _('AES')
        RSA = 'RSA', _('RSA')
        CHA = 'CHA', _('CHACHA20')
        OTHER = 'OTHER', _('其他')

    class RansomMethod(models.TextChoices):
        SINGLE = 'SINGLE', _('单重加密')
        DOUBLE = 'DOUBLE', _('双重加密')
        TRIPLE = 'TRIPLE', _('三重加密')
        QUADRUPLE = 'QUADRUPLE', _('四重加密')

    class InfectionType(models.TextChoices):
        WINDOWS = 'WINDOWS', _('WINDOWS')
        LINUX = 'LINUX', _('LINUX')
        NAS = 'NAS', _('NAS')

    name = models.CharField(_('病毒家族名称'), max_length=100)
    logo = models.ImageField(_('Logo'), upload_to='virus_family_logo/')
    first_seen_time = models.DateField(_('首次出现时间'))
    encryption_algorithm = models.CharField(
        _('加密算法'),
        max_length=10,
        choices=EncryptionAlgorithm.choices
    )
    ransom_method = models.CharField(
        _('勒索方式'),
        max_length=10,
        choices=RansomMethod.choices
    )
    infection_type = models.CharField(
        _('感染类型'),
        max_length=10,
        choices=InfectionType.choices
    )
    wallet_address = models.CharField(_('钱包地址'), max_length=100)
    public_decrypt = models.BooleanField(_('公开解密器'), default=False)
    encrypted_suffix = models.CharField(_('加密后缀'), max_length=50)
    description = models.TextField(_('家族简介'))
    attack_vector = models.TextField(_('攻击路线'))
    ioc = models.TextField(_('IOC信息'))

    class Meta:
        verbose_name = _('病毒家族')
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class RansomNote(BaseModel):
    """勒索信模型"""
    virus_family = models.ForeignKey(
        VirusFamily,
        on_delete=models.CASCADE,
        related_name='ransom_notes',
        verbose_name=_('病毒家族')
    )
    name = models.CharField(_('勒索信名称'), max_length=100)
    content = models.TextField(_('文本内容'))

    class Meta:
        verbose_name = _('勒索信')
        verbose_name_plural = verbose_name
        ordering = ['-created_at']


class RansomAddress(BaseModel):
    """勒索地址模型"""

    class AddressOption(models.TextChoices):
        TOR = 'TOR', _('TOR网络')
        TELEGRAM = 'TELEGRAM', _('Telegram')
        EMAIL = 'EMAIL', _('电子邮件')

    virus_family = models.ForeignKey(
        VirusFamily,
        on_delete=models.CASCADE,
        related_name='ransom_addresses',
        verbose_name=_('病毒家族')
    )
    address_option = models.CharField(
        _('地址选项'),
        max_length=10,
        choices=AddressOption.choices
    )
    content = models.TextField(_('文本内容'))

    class Meta:
        verbose_name = _('勒索地址')
        verbose_name_plural = verbose_name
        ordering = ['-created_at']


class Tool(BaseModel):
    """工具模型"""

    class ToolType(models.TextChoices):
        LM = 'LM', _('内网横向')
        SCAN = 'SCAN', _('内网扫描')

    virus_family = models.ForeignKey(
        VirusFamily,
        on_delete=models.CASCADE,
        related_name='tools',
        verbose_name=_('病毒家族')
    )
    name = models.CharField(_('工具名称'), max_length=100)
    tool_type = models.CharField(
        _('工具类型'),
        max_length=10,
        choices=ToolType.choices
    )
    introduction = models.TextField(_('工具介绍'))
    file = models.FileField(
        _('工具文件'), 
        upload_to='virus_family_tools/',
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _('工具')
        verbose_name_plural = verbose_name
        ordering = ['-created_at']


#病毒样本
class VirusSample(BaseModel):
    """病毒样本模型"""
    virus_family = models.ForeignKey(
        VirusFamily,
        on_delete=models.CASCADE,
        related_name='virus_samples',
        verbose_name=_('病毒家族')
    )
    name = models.CharField(_('样本名称'), max_length=100)
    file = models.FileField(
        _('样本文件'), 
        upload_to='virus_family_samples/',
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _('病毒样本')
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

class Negotiation(BaseModel):
    """谈判记录模型"""
    virus_family = models.ForeignKey(
        VirusFamily,
        on_delete=models.CASCADE,
        related_name='negotiations',
        verbose_name=_('病毒家族')
    )
    date = models.DateField(_('日期'))
    initial_amount = models.DecimalField(
        _('初始金额'),
        max_digits=10,
        decimal_places=2
    )
    final_delivery_amount = models.DecimalField(
        _('最终交付金额'),
        max_digits=10,
        decimal_places=2
    )
    is_pay = models.BooleanField(_('是否支付'), default=False)
    file = models.FileField(_('聊天记录'), upload_to='negotiation_record/')
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('谈判记录')
        verbose_name_plural = verbose_name
        ordering = ['-date']


class Victim(BaseModel):
    """受害者模型"""
    virus_family = models.ForeignKey(
        VirusFamily,
        on_delete=models.CASCADE,
        related_name='victims',
        verbose_name=_('病毒家族')
    )
    date = models.DateField(_('日期'))
    victim = models.CharField(_('受害者'), max_length=100)
    official_website = models.URLField(_('官网'))
    location = models.CharField(_('所在地区'), max_length=100)
    introduction = models.TextField(_('简介'))

    class Meta:
        verbose_name = _('受害者')
        verbose_name_plural = verbose_name
        ordering = ['-date']
