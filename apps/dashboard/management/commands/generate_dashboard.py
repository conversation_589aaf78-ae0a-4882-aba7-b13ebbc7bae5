from django.core.management.base import BaseCommand
from django.template.loader import render_to_string
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q
import os
from datetime import datetime, timedelta
from apps.exercise.models import Exercise
from apps.infection.models import InfectionRecord, DataLeakage
from apps.assets.models import Asset
from django.conf import settings
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd

class Command(BaseCommand):
    help = '生成数据看板HTML报告'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='统计天数（默认7天）'
        )
        parser.add_argument(
            '--output',
            help='输出文件路径'
        )

    def handle(self, *args, **options):
        days = options['days']
        end_time = timezone.now()
        start_time = end_time - timedelta(days=days)
        
        try:
            # 收集数据
            data = self.collect_dashboard_data(start_time, end_time)
            
            # 生成图表
            charts = self.generate_charts(data)
            
            # 渲染HTML报告
            context = {
                'data': data,
                'charts': charts,
                'start_time': start_time,
                'end_time': end_time,
                'generated_at': timezone.now()
            }
            
            html_content = render_to_string(
                'dashboard/dashboard_report.html',
                context
            )
            
            # 确定输出路径
            if options['output']:
                output_path = options['output']
            else:
                filename = f"dashboard_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                output_path = os.path.join(settings.MEDIA_ROOT, 'reports', filename)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.stdout.write(
                self.style.SUCCESS(f'数据看板报告已生成: {output_path}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'生成数据看板报告时出错: {str(e)}')
            )

    def collect_dashboard_data(self, start_time, end_time):
        """收集数据看板数据"""
        # 演练统计
        exercise_stats = Exercise.objects.filter(
            created_at__range=(start_time, end_time)
        ).aggregate(
            total=Count('id'),
            completed=Count('id', filter=Q(status__in=['FI', 'TE'])),
            active=Count('id', filter=Q(status__in=['RU', 'PA']))
        )
        
        # 感染趋势
        infection_trend = InfectionRecord.objects.filter(
            infection_time__range=(start_time, end_time)
        ).values('infection_time__date').annotate(
            count=Count('id')
        ).order_by('infection_time__date')
        
        # 部门分布
        department_stats = InfectionRecord.objects.filter(
            infection_time__range=(start_time, end_time)
        ).values('asset__department').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # 资产类型分布
        asset_type_stats = InfectionRecord.objects.filter(
            infection_time__range=(start_time, end_time)
        ).values('asset__asset_type').annotate(
            count=Count('id')
        )
        
        # 数据泄露统计
        leakage_stats = DataLeakage.objects.filter(
            detected_time__range=(start_time, end_time)
        ).aggregate(
            total=Count('id'),
            total_files=Sum('file_count'),
            total_size=Sum('data_size'),
            avg_sensitivity=Avg('sensitivity')
        )
        
        return {
            'exercises': exercise_stats,
            'infection_trend': list(infection_trend),
            'departments': list(department_stats),
            'asset_types': list(asset_type_stats),
            'data_leakage': leakage_stats
        }

    def generate_charts(self, data):
        """生成图表"""
        charts = {}
        
        # 感染趋势图
        df_trend = pd.DataFrame(data['infection_trend'])
        if not df_trend.empty:
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df_trend['infection_time__date'],
                y=df_trend['count'],
                mode='lines+markers',
                name='感染数量'
            ))
            fig.update_layout(
                title='感染趋势',
                xaxis_title='日期',
                yaxis_title='感染数量'
            )
            charts['infection_trend'] = fig.to_html(full_html=False)
        
        # 部门分布饼图
        df_dept = pd.DataFrame(data['departments'])
        if not df_dept.empty:
            fig = px.pie(
                df_dept,
                values='count',
                names='asset__department',
                title='部门感染分布'
            )
            charts['department_distribution'] = fig.to_html(full_html=False)
        
        # 资产类型分布柱状图
        df_asset = pd.DataFrame(data['asset_types'])
        if not df_asset.empty:
            fig = px.bar(
                df_asset,
                x='asset__asset_type',
                y='count',
                title='资产类型感染分布'
            )
            charts['asset_type_distribution'] = fig.to_html(full_html=False)
        
        return charts 