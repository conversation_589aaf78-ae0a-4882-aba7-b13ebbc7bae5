from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.dashboard.models import DashboardMetric

class Command(BaseCommand):
    help = '清理旧的仪表盘指标数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='保留最近几天的数据（默认30天）'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行模式，不实际删除数据'
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        cleanup_date = timezone.now() - timezone.timedelta(days=days)
        
        # 获取要删除的记录数
        metrics = DashboardMetric.objects.filter(
            metric_time__lt=cleanup_date
        )
        count = metrics.count()
        
        if dry_run:
            self.stdout.write(
                f'将删除 {count} 条 {days} 天前的指标记录（试运行模式）'
            )
        else:
            metrics.delete()
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {count} 条 {days} 天前的指标记录')
            ) 