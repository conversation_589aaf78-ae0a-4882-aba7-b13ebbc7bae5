from rest_framework import serializers
from drf_spectacular.utils import extend_schema_field
from .models import DashboardStatistics, ExerciseStatistics, AssetStatistics
from apps.exercise.models import Exercise
from apps.assets.models import AssetGroup
from .utils import DataFormatter

class DashboardStatisticsSerializer(serializers.ModelSerializer):
    """数据看板统计序列化器"""
    infection_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = DashboardStatistics
        fields = [
            'date', 'total_exercises', 'active_exercises', 
            'finished_exercises', 'total_assets', 'infected_assets',
            'total_asset_groups', 'infection_rate'
        ]
        
    def get_infection_rate(self, obj):
        """计算感染率"""
        if obj.total_assets == 0:
            return "0%"
        rate = (obj.infected_assets / obj.total_assets) * 100
        return f"{rate:.1f}%"

class ExerciseStatisticsSerializer(serializers.ModelSerializer):
    """演练统计序列化器"""
    exercise_name = serializers.CharField(source='exercise.name')
    duration_display = serializers.SerializerMethodField()
    
    class Meta:
        model = ExerciseStatistics
        fields = [
            'exercise', 'exercise_name', 'date', 'infected_count',
            'success_rate', 'duration', 'duration_display'
        ]
        
    def get_duration_display(self, obj):
        """格式化持续时间显示"""
        if not obj.duration:
            return "未开始"
        hours = obj.duration.total_seconds() // 3600
        minutes = (obj.duration.total_seconds() % 3600) // 60
        return f"{int(hours)}小时{int(minutes)}分钟"

class AssetStatisticsSerializer(serializers.ModelSerializer):
    """资产统计序列化器"""
    asset_group_name = serializers.CharField(source='asset_group.name')
    infection_rate = serializers.SerializerMethodField()
    online_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = AssetStatistics
        fields = [
            'asset_group', 'asset_group_name', 'date', 
            'total_assets', 'online_assets', 'infected_assets',
            'infection_rate', 'online_rate'
        ]
        
    def get_infection_rate(self, obj):
        """计算感染率"""
        if obj.total_assets == 0:
            return "0%"
        rate = (obj.infected_assets / obj.total_assets) * 100
        return f"{rate:.1f}%"
        
    def get_online_rate(self, obj):
        """计算在线率"""
        if obj.total_assets == 0:
            return "0%"
        rate = (obj.online_assets / obj.total_assets) * 100
        return f"{rate:.1f}%"

class DashboardOverviewSerializer(serializers.Serializer):
    """数据看板概览序列化器"""
    today_stats = DashboardStatisticsSerializer()
    exercise_trend = ExerciseStatisticsSerializer(many=True)
    asset_distribution = AssetStatisticsSerializer(many=True)
    status_distribution = serializers.DictField()
    recent_exercises = serializers.SerializerMethodField()
    
    def get_recent_exercises(self, obj):
        """获取最近的演练"""
        exercises = Exercise.objects.all().order_by('-created_at')[:5]
        return [{
            'id': ex.id,
            'name': ex.name,
            'status': ex.get_status_display(),
            'start_time': ex.start_time,
            'end_time': ex.end_time
        } for ex in exercises] 