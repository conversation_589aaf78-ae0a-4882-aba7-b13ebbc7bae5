from django import template
from django.template.defaultfilters import filesizeformat
from django.utils import timezone
from datetime import timedelta

register = template.Library()

@register.filter
def format_bytes(value):
    """格式化字节数为人类可读格式"""
    try:
        return filesizeformat(value)
    except (ValueError, TypeError):
        return '0 bytes'

@register.filter
def time_since(value):
    """计算时间差，返回人类可读格式"""
    if not value:
        return ''
    
    now = timezone.now()
    diff = now - value
    
    if diff < timedelta(minutes=1):
        return '刚刚'
    elif diff < timedelta(hours=1):
        minutes = int(diff.total_seconds() / 60)
        return f'{minutes}分钟前'
    elif diff < timedelta(days=1):
        hours = int(diff.total_seconds() / 3600)
        return f'{hours}小时前'
    elif diff < timedelta(days=30):
        days = diff.days
        return f'{days}天前'
    else:
        return value.strftime('%Y-%m-%d %H:%M')

@register.simple_tag
def infection_rate(infected, total):
    """计算感染率"""
    try:
        rate = (infected / total) * 100
        return f'{rate:.1f}%'
    except (ZeroDivisionError, TypeError):
        return '0%'

@register.inclusion_tag('dashboard/includes/status_badge.html')
def status_badge(status, text=None):
    """生成状态徽章"""
    status_classes = {
        'IN': 'badge-danger',    # 已感染
        'EN': 'badge-warning',   # 已加密
        'DE': 'badge-info',      # 已解密
        'CL': 'badge-success',   # 已清理
        'FA': 'badge-secondary', # 感染失败
    }
    
    return {
        'class': status_classes.get(status, 'badge-secondary'),
        'text': text or dict(InfectionRecord.InfectionStatus.choices).get(status, '未知')
    } 