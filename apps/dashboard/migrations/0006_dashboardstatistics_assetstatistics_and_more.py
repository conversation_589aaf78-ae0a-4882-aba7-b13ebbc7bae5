# Generated by Django 5.1.3 on 2025-02-08 11:09

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets', '0012_auto_20250106_1750'),
        ('dashboard', '0005_remove_assetstatus_asset_and_more'),
        ('exercise', '0008_rename_celery_task_id_to_task_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='统计日期')),
                ('total_exercises', models.IntegerField(default=0, verbose_name='总演练数')),
                ('active_exercises', models.IntegerField(default=0, verbose_name='进行中演练数')),
                ('finished_exercises', models.IntegerField(default=0, verbose_name='已完成演练数')),
                ('total_assets', models.IntegerField(default=0, verbose_name='总资产数')),
                ('infected_assets', models.IntegerField(default=0, verbose_name='已感染资产数')),
                ('total_asset_groups', models.IntegerField(default=0, verbose_name='资产组数')),
            ],
            options={
                'verbose_name': '数据看板统计',
                'verbose_name_plural': '数据看板统计',
                'db_table': 'ls_dashboard_statistics',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='AssetStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='统计日期')),
                ('total_assets', models.IntegerField(default=0, verbose_name='总资产数')),
                ('online_assets', models.IntegerField(default=0, verbose_name='在线资产数')),
                ('infected_assets', models.IntegerField(default=0, verbose_name='感染资产数')),
                ('asset_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='statistics', to='assets.assetgroup', verbose_name='资产组')),
            ],
            options={
                'verbose_name': '资产统计',
                'verbose_name_plural': '资产统计',
                'db_table': 'ls_asset_statistics',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='ExerciseStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='统计日期')),
                ('infected_count', models.IntegerField(default=0, verbose_name='感染数')),
                ('success_rate', models.FloatField(default=0, verbose_name='成功率')),
                ('duration', models.DurationField(null=True, verbose_name='持续时间')),
                ('exercise', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='statistics', to='exercise.exercise', verbose_name='演练')),
            ],
            options={
                'verbose_name': '演练统计',
                'verbose_name_plural': '演练统计',
                'db_table': 'ls_exercise_statistics',
                'ordering': ['-date'],
            },
        ),
    ]
