from django.db import models
from django.utils import timezone
from config.models import BaseModel
from apps.users.models import User
from apps.exercise.models import Exercise
from apps.assets.models import Asset, AssetGroup

class DashboardMetric(BaseModel):
    """数据看板指标模型"""
    metric_time = models.DateTimeField(
        '统计时间',
        default=timezone.now,
        help_text='指标统计的时间点'
    )
    infected_count = models.IntegerField(
        '感染数量',
        default=0,
        help_text='已感染资产数量'
    )

    class Meta:
        verbose_name = '数据看板指标'
        verbose_name_plural = verbose_name
        ordering = ['-metric_time']
        get_latest_by = 'metric_time'

    def __str__(self):
        return f"感染统计 - {self.metric_time}"

class DashboardStatistics(BaseModel):
    """数据看板统计模型"""
    date = models.DateField("统计日期", default=timezone.now)
    total_exercises = models.IntegerField("总演练数", default=0)
    active_exercises = models.IntegerField("进行中演练数", default=0)
    finished_exercises = models.IntegerField("已完成演练数", default=0)
    total_assets = models.IntegerField("总资产数", default=0)
    infected_assets = models.IntegerField("已感染资产数", default=0)
    total_asset_groups = models.IntegerField("资产组数", default=0)
    
    class Meta:
        db_table = "ls_dashboard_statistics"
        verbose_name = "数据看板统计"
        verbose_name_plural = verbose_name
        ordering = ["-date"]
        
    def __str__(self):
        return f"统计数据 {self.date}"

class ExerciseStatistics(BaseModel):
    """演练统计模型"""
    exercise = models.ForeignKey(
        Exercise,
        on_delete=models.CASCADE,
        related_name="statistics",
        verbose_name="演练"
    )
    date = models.DateField("统计日期", default=timezone.now)
    infected_count = models.IntegerField("感染数", default=0)
    success_rate = models.FloatField("成功率", default=0)
    duration = models.DurationField("持续时间", null=True)
    
    class Meta:
        db_table = "ls_exercise_statistics"
        verbose_name = "演练统计"
        verbose_name_plural = verbose_name
        ordering = ["-date"]
        
    def __str__(self):
        return f"{self.exercise.name} 统计 {self.date}"

class AssetStatistics(BaseModel):
    """资产统计模型"""
    asset_group = models.ForeignKey(
        AssetGroup,
        on_delete=models.CASCADE,
        related_name="statistics",
        verbose_name="资产组"
    )
    date = models.DateField("统计日期", default=timezone.now)
    total_assets = models.IntegerField("总资产数", default=0)
    online_assets = models.IntegerField("在线资产数", default=0)
    infected_assets = models.IntegerField("感染资产数", default=0)
    
    class Meta:
        db_table = "ls_asset_statistics"
        verbose_name = "资产统计"
        verbose_name_plural = verbose_name
        ordering = ["-date"]
        
    def __str__(self):
        return f"{self.asset_group.name} 统计 {self.date}"
