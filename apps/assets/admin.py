from django.contrib import admin
from .models import Asset, AssetGroup, AssetImportHistory

@admin.register(Asset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ('name', 'asset_type', 'ip_address_v4', 'department',
                   'is_active', 'os_type', 'created_by')
    list_filter = ('asset_type', 'department', 'is_active', 'os_type', 'created_at')
    search_fields = ('name', 'ip_address', 'mac_address', 'description')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_by', 'created_at', 'updated_at', 'last_seen')

@admin.register(AssetGroup)
class AssetGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_by', 'created_at')
    search_fields = ('name', 'description')
    # filter_horizontal = ('assets',)
    date_hierarchy = 'created_at'
    readonly_fields = ('created_by', 'created_at', 'updated_at')

@admin.register(AssetImportHistory)
class AssetImportHistoryAdmin(admin.ModelAdmin):
    list_display = ('file_name', 'status', 'total_count', 'success_count',
                   'failed_count', 'created_at', 'completed_at')
    list_filter = ('status', 'created_at')
    search_fields = ('file_name', 'error_message')
    date_hierarchy = 'created_at'
    readonly_fields = ('file_name', 'file_size', 'total_count', 'success_count',
                      'failed_count', 'status', 'error_message', 'created_at',
                      'completed_at')
