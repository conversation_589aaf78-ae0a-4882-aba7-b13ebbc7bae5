from django_filters import rest_framework as filters
from .models import Asset, AssetGroup
from django.db import models


class AssetGroupFilter(filters.FilterSet):
    """资产组过滤器"""
    asset_type = filters.CharFilter(
        help_text="按资产类型过滤资产组(EP:终端,SV:服务器,EM:电子邮件)，支持逗号分隔",
        method='filter_by_asset_type'
    )

    def filter_by_asset_type(self, queryset, name, value):
        if value:
            # 拆分逗号分隔的值
            asset_types = [v.strip() for v in value.split(',') if v.strip()]
            return queryset.filter(asset__asset_type__in=asset_types).distinct()
        return queryset

    class Meta:
        model = AssetGroup
        fields = ['asset_type']


class AssetFilter(filters.FilterSet):
    """资产过滤器"""
    group = filters.NumberFilter(
        field_name="group",
        help_text="按资产组ID过滤"
    )

    class Meta:
        model = Asset
        fields = ['group', 'asset_type', 'os_type', 'department', 'is_active']
