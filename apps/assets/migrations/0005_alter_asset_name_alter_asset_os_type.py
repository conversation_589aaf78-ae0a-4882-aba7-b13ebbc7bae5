# Generated by Django 5.1.3 on 2024-12-04 09:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assets', '0004_remove_asset_ip_address_remove_assetgroup_assets_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='asset',
            name='name',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='设备名称'),
        ),
        migrations.AlterField(
            model_name='asset',
            name='os_type',
            field=models.CharField(blank=True, choices=[('WIN7', 'Windows 7'), ('WIN10', 'Windows 10'), ('WIN10_W', 'Windows 10网信版'), ('WIN11', 'Windows 11'), ('CENTOS_7', 'CentOS 7'), ('WIN_SERVER', 'Windows Server'), ('KALI_LINUX', 'kali_linux'), ('IOS', 'ios'), ('MACOS', '<PERSON><PERSON>'), ('OTHER', '其他')], max_length=20, null=True, verbose_name='操作系统'),
        ),
    ]
