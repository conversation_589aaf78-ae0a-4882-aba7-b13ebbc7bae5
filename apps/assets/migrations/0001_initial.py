# Generated by Django 5.1.3 on 2024-11-21 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='资产名称')),
                ('asset_type', models.CharField(choices=[('EP', '终端'), ('SV', '服务器'), ('NW', '网络设备')], max_length=2, verbose_name='资产类型')),
                ('ip_address', models.GenericIPAddressField(unique=True, verbose_name='IP地址')),
                ('mac_address', models.CharField(blank=True, max_length=17, verbose_name='MAC地址')),
                ('os_type', models.CharField(choices=[('WIN10', 'Windows 10'), ('WIN11', 'Windows 11'), ('WIN_SERVER', 'Windows Server'), ('LINUX', 'Linux'), ('MACOS', 'MacOS'), ('OTHER', '其他')], max_length=20, verbose_name='操作系统')),
                ('os_version', models.CharField(blank=True, max_length=50, verbose_name='系统版本')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在线')),
                ('last_seen', models.DateTimeField(auto_now=True, verbose_name='最后在线时间')),
            ],
            options={
                'verbose_name': '资产',
                'verbose_name_plural': '资产',
            },
        ),
        migrations.CreateModel(
            name='AssetGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='组名')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
            ],
            options={
                'verbose_name': '资产组',
                'verbose_name_plural': '资产组',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AssetImportHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=200, verbose_name='文件名')),
                ('file_size', models.IntegerField(verbose_name='文件大小(bytes)')),
                ('total_count', models.IntegerField(default=0, verbose_name='总记录数')),
                ('success_count', models.IntegerField(default=0, verbose_name='成功数')),
                ('failed_count', models.IntegerField(default=0, verbose_name='失败数')),
                ('status', models.CharField(choices=[('PE', '等待处理'), ('PR', '处理中'), ('SU', '成功'), ('FA', '失败')], default='PE', max_length=2, verbose_name='状态')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
            ],
            options={
                'verbose_name': '资产导入历史',
                'verbose_name_plural': '资产导入历史',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AssetRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('relation_type', models.CharField(choices=[('DEP', '依赖'), ('CONN', '连接'), ('PART', '组成'), ('SYNC', '同步'), ('OTHER', '其他')], default='OTHER', max_length=5, verbose_name='关系类型')),
                ('impact_score', models.FloatField(default=0.0, help_text='0-1之间的浮点数，表示影响程度', verbose_name='影响程度')),
                ('description', models.TextField(blank=True, verbose_name='关系描述')),
            ],
            options={
                'verbose_name': '资产关系',
                'verbose_name_plural': '资产关系',
            },
        ),
        migrations.CreateModel(
            name='AssetStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cpu_usage', models.FloatField(verbose_name='CPU使用率')),
                ('memory_usage', models.FloatField(verbose_name='内存使用率')),
                ('disk_usage', models.FloatField(verbose_name='磁盘使用率')),
                ('network_in', models.BigIntegerField(verbose_name='入站流量(bytes/s)')),
                ('network_out', models.BigIntegerField(verbose_name='出站流量(bytes/s)')),
                ('process_count', models.IntegerField(verbose_name='进程数')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='记录时间')),
            ],
            options={
                'verbose_name': '资产状态',
                'verbose_name_plural': '资产状态',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='AssetTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='标签名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '资产标签',
                'verbose_name_plural': '资产标签',
            },
        ),
        migrations.CreateModel(
            name='AssetTopology',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='拓扑名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
            ],
            options={
                'verbose_name': '资产拓扑',
                'verbose_name_plural': '资产拓扑',
            },
        ),
        migrations.CreateModel(
            name='TopologyAsset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('x_pos', models.FloatField(default=0, verbose_name='X坐标')),
                ('y_pos', models.FloatField(default=0, verbose_name='Y坐标')),
                ('layer', models.IntegerField(default=0, verbose_name='层级')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='图标')),
            ],
            options={
                'verbose_name': '拓扑资产节点',
                'verbose_name_plural': '拓扑资产节点',
            },
        ),
    ]
