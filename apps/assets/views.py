import codecs
import csv

from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db.models import Count, QuerySet
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, inline_serializer
from rest_framework import mixins
from rest_framework import viewsets, permissions, status, filters, serializers
from rest_framework.decorators import action
from rest_framework.response import Response

from extensions import constants, permissions_utils
from extensions import excel_utils
from extensions import queryset_utils
from .filters import AssetFilter, AssetGroupFilter
from .models import (
    Asset, AssetGroup, AssetImportHistory
)
from .serializers import (
    AssetSerializer, AssetGroupSerializer,
    AssetImportHistorySerializer, ExerciseAssetSerializer
)
from ..exercise.models import Exercise


@extend_schema_view(
    list=extend_schema(
        summary="获取资产组列表",
        description="获取所有资产组信息",
        tags=["资产管理"],
        parameters=[
            OpenApiParameter(
                name='search',
                description='搜索关键词(组名/描述)',
                type=str,
                required=False
            ),
            OpenApiParameter(
                name='asset_type',
                description='按资产类型过滤(EP:终端,SV:服务器,EM:电子邮件)',
                type=str,
                required=False
            )
        ]
    ),
    create=extend_schema(
        summary="创建资产组",
        description="创建新的资产组",
        tags=["资产管理"]
    ),
    retrieve=extend_schema(
        summary="获取资产组详情",
        description="获取指定资产组的详细信息",
        tags=["资产管理"]
    ),
    update=extend_schema(
        summary="更新资产组",
        description="更新指定资产组的信息",
        tags=["资产管理"]
    ),
    destroy=extend_schema(
        summary="删除资产组",
        description="删除指定资产组",
        tags=["资产管理"]
    ),
    partial_update=extend_schema(
        summary="部分更新资产组",
        description="部分更新指定资产组的信息",
        tags=["资产管理"]
    ),
    available=extend_schema(
        summary="获取可用的资产组列表",
        description="获取所有可用的资产组列表",
        tags=["资产管理"]
    )
)
class AssetGroupViewSet(queryset_utils.UserOwnedModelViewSet):
    """
    资产组管理视图集
    """
    queryset = AssetGroup.objects.all()
    serializer_class = AssetGroupSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = AssetGroupFilter
    search_fields = ['name', 'description']

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset

    @extend_schema(
        parameters=[
            OpenApiParameter(name='asset_id', type=int, location=OpenApiParameter.QUERY)
        ]
    )
    @action(detail=False, methods=['get'])
    def available(self, request):
        """获取可用的资产组列表"""
        asset_id = request.query_params.get('asset_id')
        queryset = self.get_queryset()

        if asset_id:
            # 排除已经包含该资产的组
            queryset = queryset.exclude(assets__id=asset_id)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        summary="获取资产导入历史",
        description="获取所有资产导入历史记录",
        tags=["资产管理"]
    ),
    create=extend_schema(
        summary="创建资产导入历史",
        description="创建新的资产导入历史记录",
        tags=["资产管理"],
        request=inline_serializer(
            name='CreateAssetImportHistoryRequest',
            fields={
                'file_name': serializers.CharField(help_text='导入文件名称'),
                'file_size': serializers.IntegerField(help_text='文件大小(字节)'),
                'status': serializers.ChoiceField(choices=['pending', 'processing', 'completed', 'failed'], help_text='导入状态'),
                'error_message': serializers.CharField(help_text='错误信息', required=False),
            }
        ),
        responses={
            201: AssetImportHistorySerializer
        }
    ),
    retrieve=extend_schema(
        summary="获取导入历史详情",
        description="获取指定导入历史记录的详细信息",
        tags=["资产管理"]
    ),
    destroy=extend_schema(
        summary="删除导入历史",
        description="删除指定导入历史记录",
        tags=["资产管理"]
    )
)
class AssetImportHistoryViewSet(mixins.ListModelMixin,
                              mixins.RetrieveModelMixin,
                              mixins.CreateModelMixin,
                              mixins.DestroyModelMixin,
                              viewsets.GenericViewSet):
    """
    资产导入历史视图集
    """
    queryset = AssetImportHistory.objects.all()
    serializer_class = AssetImportHistorySerializer
    permission_classes = [permissions.IsAuthenticated, permissions.IsAdminUser]


@extend_schema_view(
    list=extend_schema(
        summary="获取资产列表",
        description="获取所有资产信息",
        tags=["资产管理"],
        parameters=[
            OpenApiParameter(
                name='search',
                description='搜索关键词(名称/IP/MAC/描述)',
                type=str,
                required=False
            ),
            OpenApiParameter(
                name='group',
                description='资产组ID',
                type=int,
                required=False
            ),
            OpenApiParameter(
                name='asset_type',
                description='资产类型',
                type=str,
                required=False
            ),
            OpenApiParameter(
                name='os_type',
                description='操作系统类型',
                type=str,
                required=False
            ),
            OpenApiParameter(
                name='department',
                description='部门ID',
                type=int,
                required=False
            ),
            OpenApiParameter(
                name='tag',
                description='标签ID',
                type=int,
                required=False
            ),
            OpenApiParameter(
                name='is_active',
                description='是否活跃',
                type=bool,
                required=False
            )
        ]
    ),
    create=extend_schema(
        summary="创建资产",
        description="创建新的资产",
        tags=["资产管理"]
    ),
    retrieve=extend_schema(
        summary="获取资产详情",
        description="获取指定资产的详细信息",
        tags=["资产管理"]
    ),
    update=extend_schema(
        summary="更新资产",
        description="更新指定资产的信息",
        tags=["资产管理"]
    ),
    partial_update=extend_schema(
        summary="部分更新资产",
        description="部分更新指定资产的信息",
        tags=["资产管理"]
    ),
    destroy=extend_schema(
        summary="删除资产",
        description="删除指定资产",
        tags=["资产管理"]
    ),
    import_assets=extend_schema(
        summary="批量导入资产",
        description="通过CSV文件批量导入资产",
        tags=["资产管理"],
        request=inline_serializer(
            name='ImportAssetsRequest',
            fields={
                'file': serializers.FileField(help_text='CSV文件'),
                'asset_type': serializers.ChoiceField(choices=['EP', 'SV'], help_text='资产类型EP终端SV服务器'),
                'group_id': serializers.CharField(help_text='资产组ID'),
            }
        ),
        responses={
            201: inline_serializer(
                name='ImportAssetsResponse',
                fields={
                    'message': serializers.CharField(),
                    'import_history_id': serializers.IntegerField()
                }
            )
        }
    ),
    export_assets=extend_schema(
        summary="导出资产信息",
        description="导出所有资产信息为CSV文件",
        tags=["资产管理"],
        responses={
            200: inline_serializer(
                name='ExportAssetsResponse',
                fields={
                    'file_url': serializers.CharField()
                }
            )
        }
    ),
    statistics=extend_schema(
        summary="获取资产统计信息",
        description="获取资产总数及各类型资产数量统计",
        tags=["资产管理"],
        responses={
            200: inline_serializer(
                name='StatisticsResponse',
                fields={
                    'results': serializers.DictField(
                        child=serializers.IntegerField(),
                        help_text='包含total(总数)和各资产类型的数量统计'
                    )
                }
            )
        }
    )
)
class AssetViewSet(queryset_utils.UserOwnedModelViewSet):
    """资产管理视图集"""
    queryset = Asset.objects.all()
    serializer_class = AssetSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = AssetFilter
    search_fields = ['name', 'ip_address_v4', 'mac_address', 'description', 'asset_type']

    def get_queryset(self) -> 'QuerySet[Asset]':
        """支持过滤查询"""
        queryset = super().get_queryset()
        tag = self.request.query_params.get('tag')

        if tag:
            queryset = queryset.filter(tags__id=tag)

        return queryset.order_by("-id")

    def perform_create(self, serializer: AssetSerializer) -> None:
        """创建时记录创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def import_assets(self, request) -> Response:
        """批量导入资产"""
        file = request.FILES['file']
        asset_type = request.data.get("asset_type", "")
        group_id = request.data.get("group_id", "")
        if not asset_type:
            return Response({'message': "请选择资产类型"}, status=status.HTTP_400_BAD_REQUEST)
        if not file.name.endswith('.xlsx'):
            return Response({'error': '请上传Excel后缀为xlsx格式的文件'}, status=status.HTTP_400_BAD_REQUEST)
        if asset_type in (constants.Asset_TYPE_EP, constants.Asset_TYPE_SV):
            keys = ["name", "username", "ip_address_v4", "mac_address", "description"]
            sheet_name = "服务器终端"
        else:
            keys = ["email", "username"]
            sheet_name = "邮件"
        data = excel_utils.get_data_by_sheetname(file, sheet_name, keys, skip_row=8)
        # 处理os_type
        for row in data:
            if row.get("os_type"):
                row["os_type"] = constants.ASSET_OS_TYPE.get(row["os_type"], "")
            row["group_id"] = group_id
            row["asset_type"] = asset_type
        instances = [Asset(**item) for item in data]
        Asset.objects.bulk_create(instances)
        # 创建导入历史记录
        import_history = AssetImportHistory.objects.create(
            file_name=file.name,
            file_size=file.size,
        )

        # 保存文件
        file_path = f'asset_imports/{import_history.id}_{file.name}'
        default_storage.save(file_path, ContentFile(file.read()))

        return Response({
            'message': '文件上传成功',
        }, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def export_assets(self, request) -> HttpResponse:
        """导出资产信息"""
        queryset = self.get_queryset()
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="assets.csv"'
        response.write(codecs.BOM_UTF8)

        writer = csv.writer(response)
        writer.writerow(['名称', 'IP地址', 'MAC地址', '资产类型', '操作系统',
                         '操作系统版本', '部门', '标签', '描述', '是否活跃',
                         '最后在线时间', '创建者', '创建时间', '更新时间'])

        for asset in queryset:
            writer.writerow([
                asset.name,
                asset.ip_address_v4,
                asset.mac_address,
                asset.get_asset_type_display(),
                asset.get_os_display(),
                asset.os_version,
                asset.department.name if asset.department else '',
                ','.join(tag.name for tag in asset.tags.all()),
                asset.description,
                '是' if asset.is_active else '否',
                asset.last_seen.strftime('%Y-%m-%d %H:%M:%S') if asset.last_seen else '',
                asset.created_by.username,
                asset.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                asset.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            ])

        return response

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def statistics(self, request) -> HttpResponse:
        queryset = self.get_queryset()
        statistics = queryset.values('asset_type').annotate(count=Count('id')).order_by('asset_type')
        # 计算总资产数量
        total_assets = Asset.objects.count()
        is_superuser = self.request.user.is_superuser
        if not is_superuser:
            total_assets = Asset.objects.filter(created_by=self.request.user).count()
        # 将统计信息存储在字典中，按照资产类型进行分类
        stats_dict = {
            'total': total_assets,
        }

        # 遍历统计信息，将每种资产类型的数量添加到字典中
        for stat in statistics:
            stats_dict[f"{stat['asset_type']}"] = stat['count']

        return Response({"results": stats_dict}, status=status.HTTP_200_OK)


class ExtensionsAssetView(mixins.ListModelMixin, viewsets.GenericViewSet):
    """

    """
    queryset = Asset.objects.all().order_by("-id")
    serializer_class = ExerciseAssetSerializer
    permission_classes = [permissions_utils.SelfAccessPolicy]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_class = AssetFilter

    def get_queryset(self):
        exercise_id = self.kwargs.get('exercise_id')
        exercise = get_object_or_404(Exercise, pk=exercise_id)

        # 获取所有资产组
        groups = exercise.target_groups.all()

        # 获取所有资产组下的资产
        asset_ids = Asset.objects.filter(group__in=groups, asset_type__in=['EP', 'SV']).values_list("id", flat=True)

        return self.queryset.filter(id__in=asset_ids)
