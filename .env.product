# 配置数据库
DB_NAME=RansomSimDB
DB_USER=RansomSimDB
DB_PASSWORD=FWWAp5Sa4pHbS6jw
DB_HOST=*********
DB_PORT=5432

# 是否开启调试模式
DEBUG=False

# Django安全密钥
SECRET_KEY=p8e7q4w9o2i5u6y3t0r1z8x7c4v5b6n9m2a3s4d5f6g7h8j9k0l

# 文件存储后端配置
# 可选值: oss/aliyun (阿里云OSS) 或 local (本地存储)
# 生产环境建议使用阿里云OSS
FILE_STORAGE_BACKEND=oss

# 配置阿里云 OSS
ALIYUN_OSS_ACCESS_KEY_ID=LTAI5tJYTv3aiUc86xvzaXuw
ALIYUN_OSS_ACCESS_KEY_SECRET=******************************
ALIYUN_OSS_BUCKET_NAME=ransomware-emergency
ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com

# 阿里云短信配置
ALIYUN_SMS_ACCESS_KEY_ID=LTAI5tJYTv3aiUc86xvzaXuw
ALIYUN_SMS_ACCESS_KEY_SECRET=******************************
ALIYUN_SMS_SIGN_NAME=思而听
ALIYUN_SMS_TEMPLATE_CODE=SMS_247575697
ALIYUN_SMS_REGION_ID=cn-hangzhou

# 配置 Redis
REDIS_URL=redis://1Panel-redis-mzD6:6379/0
REDIS_HOST=1Panel-redis-mzD6
REDIS_PORT=6379
REDIS_PASSWORD=redis_pZJm5s

# 仿真木马服务器地址
TROJAN_SERVER_URL=http://************:7891

# COZE基础配置
# 认证类型: token(个人访问令牌) 或 jwt(OAuth JWT)
AI_AUTH_TYPE=jwt

# 个人访问令牌认证配置
AI_ACCESS_TOKEN=pat_vFgJCJIq3IvI0rVWFI4rJjYEq8e2uel8EoeSWIh0DLuWdySzUjVpiYM4LxA1AFcF
AI_BOT_ID=7453001684009418786

# OAuth JWT认证配置
# 注意: 使用JWT认证时需要将AI_AUTH_TYPE设置为jwt并配置以下参数
AI_JWT_CLIENT_ID=1132895787494
AI_JWT_PUBLIC_KEY_ID=7zDHQ2wMcSAIVuh2GrSQtK17TEXYFqXu_XPTcyykTMw
# 私钥内容，多行文本需要使用引号包裹并保留换行
AI_JWT_PRIVATE_KEY="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# 大模型API
AI_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode
AI_MODEL=qwen-max
DASHSCOPE_API_KEY=sk-bbf60bee3c58453e98e2dc6a74dcc36a

# 邮件点击回调接口
RESERVE_EMAIL=https://admin.fanglesuo.cn/api/v1/exercises/reserve_email/

# 允许的主机列表（逗号分隔）
ALLOWED_HOSTS=virusht.sierting.com,virusqt.sierting.com,localhost,127.0.0.1,www.fanglesuo.cn,fanglesuo.cn,admin.fanglesuo.cn,***********,***********

# CORS 配置（生产环境同时支持 HTTP 和 HTTPS）
CORS_ALLOWED_ORIGINS=http://virusht.sierting.com,https://virusht.sierting.com,http://virusqt.sierting.com,https://virusqt.sierting.com,http://www.fanglesuo.cn,https://www.fanglesuo.cn,http://fanglesuo.cn,https://fanglesuo.cn,http://admin.fanglesuo.cn,https://admin.fanglesuo.cn

# CSRF 可信源配置（生产环境同时支持 HTTP 和 HTTPS）
CSRF_TRUSTED_ORIGINS=http://virusht.sierting.com,https://virusht.sierting.com,http://virusqt.sierting.com,https://virusqt.sierting.com,http://www.fanglesuo.cn,https://www.fanglesuo.cn,http://fanglesuo.cn,https://fanglesuo.cn,http://admin.fanglesuo.cn,https://admin.fanglesuo.cn

# 默认管理员账号配置
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123456