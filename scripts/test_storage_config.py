#!/usr/bin/env python
"""
测试文件存储配置脚本

该脚本用于验证 FILE_STORAGE_BACKEND 环境变量是否正确配置，
以及存储后端是否按预期工作。

使用方法：
1. 设置环境变量 DJANGO_SETTINGS_MODULE=config.settings
2. 运行脚本：python scripts/test_storage_config.py [storage_backend]
   - storage_backend: 可选参数，指定要测试的存储后端 (local/oss)

示例：
- python scripts/test_storage_config.py local
- python scripts/test_storage_config.py oss
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))
sys.path.insert(0, str(BASE_DIR / "apps"))

# 处理命令行参数
if len(sys.argv) > 1:
    storage_backend = sys.argv[1].lower()
    if storage_backend in ['local', 'oss', 'aliyun']:
        os.environ['FILE_STORAGE_BACKEND'] = storage_backend
        print(f"设置测试存储后端为: {storage_backend}")

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# 初始化Django
django.setup()

from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile


def test_storage_configuration():
    """测试存储配置"""
    print("=" * 60)
    print("文件存储配置测试")
    print("=" * 60)
    
    # 显示当前配置
    file_storage_backend = getattr(settings, 'FILE_STORAGE_BACKEND', 'unknown')
    default_file_storage_backend = getattr(settings, 'DEFAULT_FILE_STORAGE_BACKEND', 'unknown')
    
    print(f"环境变量 FILE_STORAGE_BACKEND: {file_storage_backend}")
    print(f"选择的存储后端: {default_file_storage_backend}")
    print(f"实际存储类: {default_storage.__class__.__module__}.{default_storage.__class__.__name__}")
    
    # 显示STORAGES配置
    storages_config = getattr(settings, 'STORAGES', {})
    print(f"STORAGES配置: {storages_config}")
    
    print("\n" + "-" * 40)
    
    # 根据配置类型显示相关信息
    if file_storage_backend in ["oss", "aliyun"]:
        print("当前配置：阿里云OSS存储")
        print("相关配置：")
        aliyun_oss_config = getattr(settings, 'ALIYUN_OSS', {})
        for key, value in aliyun_oss_config.items():
            if 'SECRET' in key.upper():
                # 隐藏敏感信息
                print(f"  {key}: {'*' * len(str(value)) if value else 'None'}")
            else:
                print(f"  {key}: {value}")
    else:
        print("当前配置：本地文件存储")
        print("相关配置：")
        print(f"  MEDIA_ROOT: {getattr(settings, 'MEDIA_ROOT', 'None')}")
        print(f"  MEDIA_URL: {getattr(settings, 'MEDIA_URL', 'None')}")
    
    print("\n" + "-" * 40)
    return file_storage_backend, default_file_storage_backend


def test_file_operations():
    """测试文件操作"""
    print("测试文件操作...")
    
    try:
        # 创建测试文件
        test_content = "这是一个测试文件，用于验证存储配置是否正常工作。\nTest file for storage configuration validation."
        test_file = ContentFile(test_content.encode('utf-8'))
        test_filename = "test/storage_test.txt"
        
        # 保存文件
        print(f"正在保存测试文件: {test_filename}")
        saved_path = default_storage.save(test_filename, test_file)
        print(f"文件保存成功，路径: {saved_path}")
        
        # 获取文件URL
        file_url = default_storage.url(saved_path)
        print(f"文件访问URL: {file_url}")
        
        # 检查文件是否存在
        exists = default_storage.exists(saved_path)
        print(f"文件存在性检查: {exists}")
        
        # 获取文件大小
        if exists:
            file_size = default_storage.size(saved_path)
            print(f"文件大小: {file_size} 字节")
        
        # 读取文件内容
        if exists:
            try:
                with default_storage.open(saved_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"文件内容预览: {content[:50]}...")
            except Exception as e:
                print(f"读取文件内容时出现编码问题: {str(e)}")
                # 尝试以二进制模式读取
                try:
                    with default_storage.open(saved_path, 'rb') as f:
                        content = f.read()
                        print(f"文件内容预览（二进制）: {content[:50]}...")
                except Exception as e2:
                    print(f"二进制读取也失败: {str(e2)}")
        
        # 清理测试文件
        print("正在清理测试文件...")
        if exists:
            default_storage.delete(saved_path)
            print("测试文件已删除")
        
        print("✅ 文件操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("开始测试文件存储配置...\n")
    
    # 测试配置
    file_storage_backend, default_file_storage_backend = test_storage_configuration()
    
    # 测试文件操作
    success = test_file_operations()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("✅ 所有测试通过")
        print(f"当前使用的存储后端: {file_storage_backend}")
        print(f"存储类: {default_file_storage_backend}")
    else:
        print("❌ 测试失败")
        print("请检查配置和环境变量设置")
    
    print("\n配置说明：")
    print("- 设置 FILE_STORAGE_BACKEND=oss 使用阿里云OSS")
    print("- 设置 FILE_STORAGE_BACKEND=local 使用本地存储")
    print("- 不设置该变量默认使用本地存储")


if __name__ == "__main__":
    main()
