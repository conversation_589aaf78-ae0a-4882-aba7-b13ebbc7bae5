#!/usr/bin/env python
"""Django的命令行实用程序，用于管理任务。"""
import os
import sys


def main():
    """运行管理任务。"""
    os.environ.setdefault('DJANGO_ENV', 'develop')
    # 设置默认的Django设置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    try:
        # 尝试从django.core.management导入execute_from_command_line
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        # 如果导入失败，抛出ImportError异常，并提供可能的原因
        raise ImportError(
            "无法导入Django。您确定它已安装并且在PYTHONPATH环境变量中可用吗？您是否忘记了激活虚拟环境？"
        ) from exc
    # 执行命令行参数
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    # 如果该脚本是主程序，则调用main函数
    main()
