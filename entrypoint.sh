#!/bin/bash

# 设置错误时退出
set -e

echo "开始启动Django应用..."

# 确保日志目录存在并有正确的权限（以root身份执行）
echo "设置目录权限..."
mkdir -p /app/logs /app/media /app/static /tmp
# 设置logs目录权限，允许所有用户写入应用日志
chmod -R 777 /app/logs
# 确保整个应用目录有正确的权限
chown -R appuser:appuser /app
chmod -R 755 /app
# 确保Python可执行文件有执行权限
chmod +x /usr/local/bin/python*
# 确保appuser可以访问必要的系统目录
chmod 755 /tmp

# 等待数据库可用
echo "等待数据库连接..."
python << END
import os
import sys
import time
import psycopg2
from psycopg2 import OperationalError

def wait_for_db():
    """等待数据库可用"""
    db_config = {
        'host': os.getenv('DB_HOST'),
        'port': os.getenv('DB_PORT', '5432'),
        'user': os.getenv('DB_USER'),
        'password': os.getenv('DB_PASSWORD'),
        'database': os.getenv('DB_NAME')
    }

    max_retries = 30
    retry_count = 0

    while retry_count < max_retries:
        try:
            conn = psycopg2.connect(**db_config)
            conn.close()
            print("数据库连接成功!")
            return True
        except OperationalError as e:
            retry_count += 1
            print(f"数据库连接失败 (尝试 {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                print("等待5秒后重试...")
                time.sleep(5)
            else:
                print("数据库连接超时，退出...")
                sys.exit(1)

if __name__ == "__main__":
    wait_for_db()
END

# 执行数据库迁移
echo "执行数据库迁移..."
su appuser -c "python manage.py makemigrations"
su appuser -c "python manage.py migrate"

echo "数据库迁移完成!"

# 创建默认超级管理员
echo "创建默认超级管理员..."
su appuser -c "python manage.py create_superuser"

echo "超级管理员创建完成!"

# 启动supervisor（以root身份运行supervisor，但子进程会以appuser身份运行）
echo "启动应用服务..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
