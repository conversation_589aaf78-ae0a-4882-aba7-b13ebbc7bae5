services:
  web:
    build: .
    restart: unless-stopped
    environment:
      - DJANGO_ENV=product
    volumes:
      - ./media:/app/media
      - ./static:/app/static
      - ./logs:/app/logs
    ports:
      - "8001:8001"
    networks:
        - 1panel-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/schema/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
    1panel-network:
        external: true
