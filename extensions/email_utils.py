import smtplib
import ssl
import uuid
from email.encoders import encode_base64
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
import requests
from email.mime.text import MIMEText
from email.utils import formataddr, parseaddr, encode_rfc2231
from email.header import Header
from email import policy
from email.parser import BytesParser
from django.utils.html import escape


def send_email(mail_host, mail_user, mail_pass, subject, content,  # NOQA
               receivers=None, ignore_cert_errors=False, port=465, email_headers=None, oss_files=None): # NOQA
    """
    发送邮件方法，支持携带多个附件从 OSS 路径下载。

    :param mail_host: SMTP 服务器地址
    :param mail_user: 发件人邮箱
    :param mail_pass: 发件人邮箱密码或授权码
    :param subject: 邮件主题
    :param content: 邮件内容 (HTML 格式)
    :param receivers: 收件人列表
    :param ignore_cert_errors: 是否忽略证书错误
    :param port: SMTP 端口
    :param email_headers: 自定义邮件头
    :param oss_files: 附件文件的 OSS 路径列表
    :return: (状态, 信息)
    """
    # mail_msg = f"""{content}"""
    # message = MIMEText(mail_msg, 'plain', 'utf-8')
    message = MIMEMultipart()
    message.attach(MIMEText(content, 'html', 'utf-8'))  # 添加邮件正文

    message["From"] = formataddr(parseaddr(mail_user))
    message['To'] = ",".join(receivers)


    message['Subject'] = Header(subject, 'utf-8')  # NOQA

    if not email_headers:
        email_headers = []
    for r_data in email_headers:
        message[r_data['x_custom_header']] = r_data['gophish']

    # 下载并附加 OSS 文件
    if oss_files:
        for file_url in oss_files:
            try:
                response = requests.get(file_url)
                if response.status_code == 200:
                    # 提取文件名
                    filename = file_url.split("/")[-1]
                    # 创建附件
                    attachment = MIMEBase('application', 'octet-stream')
                    attachment.set_payload(response.content)
                    encode_base64(attachment)
                    # 添加头部信息
                    filename_encoded = Header(filename, 'utf-8').encode()
                    attachment.add_header('Content-Disposition', f'attachment; filename="{filename_encoded}"')
                    message.attach(attachment)
                else:
                    print(f"无法下载文件: {file_url}, 状态码: {response.status_code}")
            except Exception as e:
                print(f"下载或附加文件失败: {file_url}, 错误: {e}")

    # 创建SSL上下文
    if ignore_cert_errors:
        ssl._create_default_https_context = ssl._create_unverified_context  # NOQA
    try:
        smtp_obj = smtplib.SMTP_SSL(mail_host, timeout=10)
        smtp_obj.connect(mail_host, port=port)  # 465 为 SMTP 端口号
        smtp_obj.login(mail_user, mail_pass)
        smtp_obj.sendmail(mail_user, receivers, message.as_string())
        return True, True
    except smtplib.SMTPAuthenticationError as e:
        print(f"认证错误: {e}")
        return False, "认证错误"
    except smtplib.SMTPConnectError as e:
        print(f"连接错误: {e}")
        return False, "连接错误"
    except smtplib.SMTPServerDisconnected as e:
        print(f"服务器断开连接: {e}")
        return False, "服务器断开连接"
    except smtplib.SMTPRecipientsRefused as e:
        print(f"收件人被拒绝: {e}")
        return False, "收件人被拒绝"
    except smtplib.SMTPDataError as e:
        print(f"数据传输错误: {e}")
        return False, "数据传输错误"
    except smtplib.SMTPHeloError as e:
        print(f"HELO 命令错误: {e}")
        return False, "HELO 命令错误"
    except smtplib.SMTPException as e:
        print(f"SMTP 错误: {e}")
        return False, "SMTP错误"
    except Exception as e:
        return False, e.__str__()


def parse_eml_file(eml_file):
    """
    从上传的文件对象中解析 .eml 文件并返回转义后的 HTML 内容。

    :param eml_file: InMemoryUploadedFile, 上传的 .eml 文件对象
    :return: str, 转义后的 HTML 字符串
    """
    try:
        # 读取 .eml 文件内容
        msg = BytesParser(policy=policy.default).parse(eml_file)

        # 提取 HTML 内容
        for part in msg.walk():
            if part.get_content_type() == "text/html":
                html_content = part.get_payload(decode=True).decode(part.get_content_charset())
                return html_content  # 转义 HTML

        # 如果没有 HTML，则提取纯文本内容
        for part in msg.walk():
            if part.get_content_type() == "text/plain":
                text_content = part.get_payload(decode=True).decode(part.get_content_charset())
                return text_content  # 转义纯文本内容

        return escape("无法提取有效内容")
    except Exception as e:
        return escape(f"解析文件失败: {str(e)}")


if __name__ == '__main__':
    mail_host = "smtp.feishu.cn"
    mail_user = "<EMAIL>"
    mail_pass = "dDxHr6txBaHiqzFI"
    # 目标 URL
    url = "https://ransomware-emergency.oss-cn-qingdao.aliyuncs.com/email_template/网络安全竞赛平台-软件开发项目资料.rar"
    subject = "测试"
    content = """
    你是个好人<img src="https://virusht.sierting.com/api/v1/exercises/reserve_email/?token=77659" width="1" height="1">
    """
    receivers = ["<EMAIL>"]
    email_headers = {"X-Custom-Header": "CustomValue"}
    flag, detail = send_email(mail_host, mail_user, mail_pass, subject, content, receivers, ignore_cert_errors=True,
                              port=465, oss_files=[])

    print(detail)
