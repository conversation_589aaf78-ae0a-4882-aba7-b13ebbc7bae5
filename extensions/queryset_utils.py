from rest_framework import viewsets


class UserOwnedModelViewSet(viewsets.ModelViewSet):
    """用于过滤只属于当前用户的数据的基类视图集"""

    def get_queryset(self):
        """过滤只属于当前用户的数据"""
        queryset = super().get_queryset()
        if self.request.user.is_anonymous:
            return queryset
        is_superuser = self.request.user.is_superuser
        if is_superuser:
            return queryset
        return queryset.filter(created_by=self.request.user)
