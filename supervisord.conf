[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

; 禁用公开的inet HTTP服务器
[inet_http_server]         ; inet (TCP) server disabled by default
port=0.0.0.0:9002        ; 只允许本地连接
username=admin             ; 设置用户名
password=set123456 ; 设置强密码

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
nodaemon=true
silent=false
logfile=/dev/stdout
logfile_maxbytes=0
pidfile=/tmp/supervisord.pid
loglevel=info

[program:django]
command=/usr/local/bin/gunicorn config.wsgi:application --bind 0.0.0.0:8001 --workers 4 --timeout 60 --keep-alive 5 --max-requests 1000 --max-requests-jitter 50
directory=/app
environment=PATH="/usr/local/bin:/usr/bin:/bin"
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/gunicorn.log
stderr_logfile=/app/logs/gunicorn_error.log
stopasgroup=true
killasgroup=true

[program:qcluster]
command=/usr/local/bin/python manage.py qcluster
directory=/app
environment=PATH="/usr/local/bin:/usr/bin:/bin"
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/qcluster.log
stderr_logfile=/app/logs/qcluster_error.log
stopasgroup=true
killasgroup=true

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock