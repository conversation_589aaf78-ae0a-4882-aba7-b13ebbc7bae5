[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700

; 禁用公开的inet HTTP服务器
; [inet_http_server]         ; inet (TCP) server disabled by default
; port=127.0.0.1:9002        ; 只允许本地连接
; username=admin             ; 设置用户名
; password=StrongPassword123 ; 设置强密码

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisord]
nodaemon=true
logfile=/app/logs/supervisord.log
pidfile=/tmp/supervisord.pid
loglevel=info
user=root

[program:django]
command=gunicorn config.wsgi:application --bind 0.0.0.0:8001 --workers 4 --timeout 60 --keep-alive 5 --max-requests 1000 --max-requests-jitter 50
directory=/app
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/gunicorn.log
stopasgroup=true
killasgroup=true

[program:qcluster]
command=python manage.py qcluster
directory=/app
user=appuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/qcluster.log
stopasgroup=true
killasgroup=true

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock