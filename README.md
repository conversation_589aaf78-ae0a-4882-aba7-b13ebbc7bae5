# 勒索病毒模拟演练平台后端

## 项目简介
这是一个基于Django开发的勒索病毒模拟演练平台后端服务。该系统提供病毒样本管理、病毒家族分析、钓鱼网站检测等功能,并集成了用户管理、权限控制等基础服务。

## 技术栈
- Python 3.x
- Django 5.1.3
- Django REST Framework 3.15.2
- Django Q2 1.6.1
- PostgreSQL
- Redis
- Docker & Docker Compose

## 主要功能模块
- 用户管理 (apps/users)
- 病毒检测 (apps/virus)
- 病毒家族分析 (apps/virus_family)
- 钓鱼网站检测 (apps/phishing)
- 感染分析 (apps/infection)
- 系统管理 (apps/system)
- 资产管理 (apps/assets)
- 仪表盘 (apps/dashboard)
- 练习模块 (apps/exercise)

## 项目结构
```
├── apps/                   # 应用目录
│   ├── users/             # 用户管理
│   ├── virus/             # 病毒检测
│   ├── virus_family/      # 病毒家族
│   ├── phishing/          # 钓鱼检测
│   ├── infection/         # 感染分析
│   ├── system/            # 系统管理
│   ├── assets/            # 资产管理
│   ├── dashboard/         # 数据面板
│   └── exercise/          # 练习模块
├── config/                 # 项目配置
├── docs/                   # 文档
├── extensions/            # 扩展模块
├── media/                 # 媒体文件
├── templates/             # 模板文件
├── logs/                  # 日志文件
├── .env.develop          # 开发环境配置
├── .env.product          # 生产环境配置
├── requirements.txt      # 项目依赖
├── Dockerfile            # Docker构建文件
└── docker-compose.yml    # Docker编排文件
```

## 环境要求
- Python 3.x
- PostgreSQL
- Redis
- 阿里云OSS(可选，用于文件存储)
- 阿里云短信服务(可选)

## 安装部署
1. 克隆项目
```bash
git clone [项目地址]
cd Backend
```

2. 创建虚拟环境
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
- .env.develop 为开发环境配置文件
- .env.product 为生产环境配置文件
- 修改相关配置(数据库、Redis、文件存储等)

### 重要环境变量配置

#### 数据库配置
```bash
DB_NAME=数据库名
DB_USER=数据库用户
DB_PASSWORD=数据库密码
DB_HOST=数据库主机
DB_PORT=数据库端口
```

#### 文件存储配置
```bash
# 文件存储后端选择
FILE_STORAGE_BACKEND=local  # 可选值: local(本地存储) 或 oss(阿里云OSS)

# 使用阿里云OSS时需要配置以下参数
ALIYUN_OSS_ACCESS_KEY_ID=your_access_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_OSS_BUCKET_NAME=your_bucket_name
ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com
```

#### Redis配置
```bash
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

5. 数据库迁移
```bash
python manage.py migrate
```

6. 创建管理员账号(可选)
```bash
python manage.py createsuperuser
```

7. 测试文件存储配置(可选)
```bash
# 测试本地存储
python scripts/test_storage_config.py local

# 测试阿里云OSS存储
python scripts/test_storage_config.py oss
```

8. 启动开发服务器
```bash
python manage.py runserver
```

## Docker部署
1. 构建镜像
```bash
docker-compose build
```

2. 启动服务
```bash
docker-compose up -d
```

## 文件存储配置

项目支持通过环境变量 `FILE_STORAGE_BACKEND` 动态配置文件存储方式：

- `FILE_STORAGE_BACKEND=local` - 使用本地文件存储（开发环境推荐）
- `FILE_STORAGE_BACKEND=oss` - 使用阿里云OSS存储（生产环境推荐）

使用阿里云OSS时需要配置相关环境变量：`ALIYUN_OSS_ACCESS_KEY_ID`、`ALIYUN_OSS_ACCESS_KEY_SECRET`、`ALIYUN_OSS_BUCKET_NAME`、`ALIYUN_OSS_ENDPOINT`

详细配置说明请参考：[docs/FILE_STORAGE_CONFIG.md](docs/FILE_STORAGE_CONFIG.md)

## 主要API接口
- 用户认证: `/api/auth/`
- 病毒检测: `/api/virus/`
- 病毒家族: `/api/virus-family/`
- 钓鱼检测: `/api/phishing/`
- 感染分析: `/api/infection/`
- 系统管理: `/api/system/`
- 资产管理: `/api/assets/`
- 数据面板: `/api/dashboard/`

## 开发团队
[团队信息]

## 许可证
[许可证信息] 

# Django Q2 任务队列配置说明

## 环境要求

- Python 3.10+
- Redis 6.0+
- Django Q2 1.6.1+

## 安装步骤

1. 安装Redis
   - Windows: 从 https://github.com/microsoftarchive/redis/releases 下载安装包
   - Linux/Mac: 使用包管理器安装
   ```bash
   # Ubuntu/Debian
   sudo apt-get install redis-server
   
   # CentOS/RHEL
   sudo yum install redis
   
   # Mac
   brew install redis
   ```

2. 安装Python依赖
   ```bash
   pip install -r requirements.txt
   ```

## 配置说明

1. Django Q2配置位于 `config/settings.py`
   ```python
   Q_CLUSTER = {
       'name': 'virus_sierting',
       'workers': 4,
       'recycle': 500,
       'timeout': 60,
       'compress': True,
       'save_limit': 250,
       'queue_limit': 500,
       'cpu_affinity': 1,
       'label': 'Django Q2',
       'redis': {
           'host': os.getenv('REDIS_HOST', 'localhost'),
           'port': int(os.getenv('REDIS_PORT', 6379)),
           'db': 0,
       }
   }
   ```

2. 定时任务配置
   ```python
   # 在任务文件中配置
   from django_q.tasks import schedule
   from django_q.models import Schedule
   
   # 创建定时任务
   schedule(
       'apps.system.tasks.cleanup_old_logs',  # 任务函数路径
       args=(30,),  # 任务参数
       schedule_type=Schedule.DAILY,  # 每天执行
       next_run=timezone.now()  # 下次执行时间
   )
   ```

## 启动命令

1. 启动Redis服务
   ```bash
   # Linux/Mac
   sudo service redis start
   
   # Windows
   # Redis服务应该已经自动启动
   ```

2. 启动Django Q2集群
   ```bash
   python manage.py qcluster
   ```

## 使用说明

1. 创建定时演练
   - 在创建演练时设置未来的开始时间
   - 系统会自动创建定时任务
   - 到达指定时间后自动执行

2. 演练状态说明
   - PENDING: 待开始
   - RUNNING: 进行中
   - PAUSED: 已暂停
   - FINISHED: 已完成
   - TERMINATED: 已终止

3. 任务监控
   - 可以通过Django管理界面查看任务状态
   - 任务执行结果会更新到数据库

## 注意事项

1. 服务启动顺序
   - 确保Redis服务已启动
   - 启动Django Q2集群

2. 常见问题处理
   - 如果任务没有执行,检查qcluster是否正常运行
   - 检查Redis连接是否正常
   - 查看Django日志了解详细错误信息

## 开发说明

1. 添加新的定时任务
   ```python
   # 在相应的app中创建tasks.py
   def your_task():
       # 任务代码
       pass

   # 创建定时任务
   from django_q.tasks import schedule
   from django_q.models import Schedule
   
   schedule(
       'app.tasks.your_task',
       schedule_type=Schedule.HOURLY,  # 每小时执行
   )
   ```

2. 手动触发任务
   ```python
   from django_q.tasks import async_task
   
   # 立即执行
   task_id = async_task('app.tasks.your_task')
   
   # 定时执行
   from django_q.tasks import schedule
   from django_q.models import Schedule
   
   schedule(
       'app.tasks.your_task',
       schedule_type=Schedule.ONCE,
       next_run=future_time
   )
   ``` 